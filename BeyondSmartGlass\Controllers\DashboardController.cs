using BeyondSmartGlass.Models;
using BeyondSmartGlass.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;

namespace BeyondSmartGlass.Controllers
{
    [Authorize]
    public class DashboardController : Controller
    {
        private readonly IDashboardService _dashboardService;
        private readonly IPermissionService _permissionService;
        private readonly UserManager<ApplicationUser> _userManager;

        public DashboardController(
            IDashboardService dashboardService,
            IPermissionService permissionService,
            UserManager<ApplicationUser> userManager)
        {
            _dashboardService = dashboardService;
            _permissionService = permissionService;
            _userManager = userManager;
        }

        public async Task<IActionResult> Index()
        {
            var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);
            if (userId == null)
                return RedirectToAction("Login", "Account");

            // Check if user has dashboard access
            if (!await _permissionService.HasPermissionAsync(userId, AccessRights.CanAccessDashboard))
            {
                TempData["ErrorMessage"] = "You don't have permission to access the dashboard.";
                return RedirectToAction("Index", "Home");
            }

            // Default to current month
            var startDate = new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1);
            var endDate = startDate.AddMonths(1).AddDays(-1);

            var dashboardData = await _dashboardService.GetDashboardDataAsync(startDate, endDate);
            var monthlyData = await _dashboardService.GetMonthlyDataAsync(DateTime.Now.Year);

            var viewModel = new DashboardViewModel
            {
                DashboardData = dashboardData,
                MonthlyData = monthlyData,
                SelectedPeriod = "monthly",
                StartDate = startDate,
                EndDate = endDate,
                SelectedYear = DateTime.Now.Year
            };

            return View(viewModel);
        }

        [HttpPost]
        public async Task<IActionResult> FilterData(string period, DateTime? startDate, DateTime? endDate, int? year)
        {
            var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);
            if (userId == null || !await _permissionService.HasPermissionAsync(userId, AccessRights.CanAccessDashboard))
            {
                return Json(new { success = false, message = "Access denied" });
            }

            DateTime filterStartDate, filterEndDate;
            int selectedYear = year ?? DateTime.Now.Year;

            switch (period?.ToLower())
            {
                case "weekly":
                    var startOfWeek = DateTime.Now.AddDays(-(int)DateTime.Now.DayOfWeek);
                    filterStartDate = startOfWeek;
                    filterEndDate = startOfWeek.AddDays(6);
                    break;
                case "monthly":
                    filterStartDate = new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1);
                    filterEndDate = filterStartDate.AddMonths(1).AddDays(-1);
                    break;
                case "yearly":
                    filterStartDate = new DateTime(selectedYear, 1, 1);
                    filterEndDate = new DateTime(selectedYear, 12, 31);
                    break;
                case "custom":
                    filterStartDate = startDate ?? DateTime.Now.AddMonths(-1);
                    filterEndDate = endDate ?? DateTime.Now;
                    break;
                default:
                    filterStartDate = new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1);
                    filterEndDate = filterStartDate.AddMonths(1).AddDays(-1);
                    break;
            }

            var dashboardData = await _dashboardService.GetDashboardDataAsync(filterStartDate, filterEndDate);
            var monthlyData = await _dashboardService.GetMonthlyDataAsync(selectedYear);
            var contractStatusData = await _dashboardService.GetContractStatusDataAsync(filterStartDate, filterEndDate);
            var projectStatusData = await _dashboardService.GetProjectStatusDataAsync(filterStartDate, filterEndDate);
            var maintenanceStatusData = await _dashboardService.GetMaintenanceStatusDataAsync(filterStartDate, filterEndDate);

            return Json(new
            {
                success = true,
                data = new
                {
                    dashboardData,
                    monthlyData,
                    contractStatusData,
                    projectStatusData,
                    maintenanceStatusData,
                    period,
                    startDate = filterStartDate.ToString("yyyy-MM-dd"),
                    endDate = filterEndDate.ToString("yyyy-MM-dd"),
                    year = selectedYear
                }
            });
        }

        [HttpGet]
        public async Task<IActionResult> GetChartData(string chartType, DateTime startDate, DateTime endDate)
        {
            var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);
            if (userId == null || !await _permissionService.HasPermissionAsync(userId, AccessRights.CanAccessDashboard))
            {
                return Json(new { success = false, message = "Access denied" });
            }

            List<ChartData> chartData = new();

            switch (chartType?.ToLower())
            {
                case "contracts":
                    chartData = await _dashboardService.GetContractStatusDataAsync(startDate, endDate);
                    break;
                case "projects":
                    chartData = await _dashboardService.GetProjectStatusDataAsync(startDate, endDate);
                    break;
                case "maintenance":
                    chartData = await _dashboardService.GetMaintenanceStatusDataAsync(startDate, endDate);
                    break;
            }

            return Json(new { success = true, data = chartData });
        }

        [HttpGet]
        public async Task<IActionResult> ExportData(string format, DateTime startDate, DateTime endDate)
        {
            var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);
            if (userId == null || !await _permissionService.HasPermissionAsync(userId, AccessRights.CanAccessDashboard))
            {
                return Forbid();
            }

            var dashboardData = await _dashboardService.GetDashboardDataAsync(startDate, endDate);

            if (format?.ToLower() == "csv")
            {
                var csv = GenerateCsvReport(dashboardData, startDate, endDate);
                var bytes = System.Text.Encoding.UTF8.GetBytes(csv);
                return File(bytes, "text/csv", $"dashboard-report-{startDate:yyyy-MM-dd}-to-{endDate:yyyy-MM-dd}.csv");
            }

            // Default to JSON
            return Json(dashboardData);
        }

        private string GenerateCsvReport(DashboardData data, DateTime startDate, DateTime endDate)
        {
            var csv = new System.Text.StringBuilder();
            csv.AppendLine("Dashboard Report");
            csv.AppendLine($"Period: {startDate:yyyy-MM-dd} to {endDate:yyyy-MM-dd}");
            csv.AppendLine();
            csv.AppendLine("Metric,Value");
            csv.AppendLine($"Total Contracts,{data.TotalContracts}");
            csv.AppendLine($"Total Quotations,{data.TotalQuotations}");
            csv.AppendLine($"Open Maintenance Requests,{data.OpenMaintenanceRequests}");
            csv.AppendLine($"Closed Maintenance Requests,{data.ClosedMaintenanceRequests}");
            csv.AppendLine($"Active Projects,{data.ActiveProjects}");
            csv.AppendLine($"Closed Projects,{data.ClosedProjects}");
            csv.AppendLine($"Total Sales Amount,{data.TotalSalesAmount:C}");

            return csv.ToString();
        }
    }


}
