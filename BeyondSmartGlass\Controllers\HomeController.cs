﻿using System.Diagnostics;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using BeyondSmartGlass.Models;
using BeyondSmartGlass.Models.ViewModels;
using BeyondSmartGlass.Data;

namespace BeyondSmartGlass.Controllers;

public class HomeController : Controller
{
    private readonly ILogger<HomeController> _logger;
    private readonly ApplicationDbContext _context;

    public HomeController(ILogger<HomeController> logger, ApplicationDbContext context)
    {
        _logger = logger;
        _context = context;
    }

    public async Task<IActionResult> Index()
    {
        if (!User.Identity?.IsAuthenticated ?? true)
        {
            return View("Welcome");
        }

        var dashboardData = new DashboardViewModel();

        try
        {
            // Get KPIs
            dashboardData.KPIs = new DashboardKPIs
            {
                TotalProjects = await _context.Projects.CountAsync(),
                ActiveProjects = await _context.Projects.CountAsync(p => p.Status == ProjectStatus.InProgress),
                CompletedProjects = await _context.Projects.CountAsync(p => p.Status == ProjectStatus.Delivered),
                PendingQuotations = await _context.Quotations.CountAsync(q => q.Status == QuotationStatus.Pending),
                ActiveContracts = await _context.Contracts.CountAsync(c => c.Status == ContractStatus.Active),
                PendingInstallations = await _context.Installations.CountAsync(i => i.Status == InstallationStatus.Scheduled),
                ActiveWarranties = await _context.Warranties.CountAsync(w => w.Status == WarrantyStatus.Active),
                OpenMaintenanceTickets = await _context.MaintenanceTickets.CountAsync(m => m.Status == MaintenanceTicketStatus.Open),
                TotalRevenue = await _context.Contracts.Where(c => c.Status == ContractStatus.Completed).SumAsync(c => c.TotalCost),
                PendingRevenue = await _context.Contracts.Where(c => c.Status == ContractStatus.Active).SumAsync(c => c.TotalCost)
            };

            // Get project status summary
            dashboardData.ProjectsByStatus = await _context.Projects
                .GroupBy(p => p.Status)
                .Select(g => new ProjectStatusSummary
                {
                    Status = g.Key,
                    Count = g.Count(),
                    StatusName = g.Key.ToString(),
                    Color = GetStatusColor(g.Key)
                })
                .ToListAsync();

            // Get material shortages
            dashboardData.MaterialShortages = await _context.Materials
                .Where(m => m.CurrentStock <= m.MinimumStock && m.IsActive)
                .Select(m => new MaterialShortage
                {
                    MaterialId = m.Id,
                    MaterialName = m.ItemName,
                    ItemCode = m.ItemCode,
                    CurrentStock = m.CurrentStock,
                    MinimumStock = m.MinimumStock,
                    ShortageQuantity = m.MinimumStock - m.CurrentStock,
                    Unit = m.Unit
                })
                .Take(10)
                .ToListAsync();

            // Get pending installations
            dashboardData.PendingInstallations = await _context.Installations
                .Include(i => i.Project)
                .ThenInclude(p => p.Customer)
                .Include(i => i.AssignedTeamLead)
                .Where(i => i.Status == InstallationStatus.Scheduled)
                .Select(i => new PendingInstallation
                {
                    InstallationId = i.Id,
                    ProjectId = i.ProjectId,
                    ProjectName = i.Project.ProjectName,
                    CustomerName = i.Project.Customer.Name,
                    Location = i.Project.Location,
                    ScheduledDate = i.ScheduledDate,
                    AssignedTeamLead = i.AssignedTeamLead != null ? i.AssignedTeamLead.FullName : null,
                    DaysOverdue = i.ScheduledDate.HasValue && i.ScheduledDate < DateTime.Today ?
                        (DateTime.Today - i.ScheduledDate.Value).Days : 0
                })
                .OrderBy(i => i.ScheduledDate)
                .Take(10)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading dashboard data");
            // Return empty dashboard if database is not ready
        }

        return View(dashboardData);
    }

    private static string GetStatusColor(ProjectStatus status)
    {
        return status switch
        {
            ProjectStatus.NotStarted => "secondary",
            ProjectStatus.InProgress => "primary",
            ProjectStatus.WaitingMaterials => "warning",
            ProjectStatus.Installed => "info",
            ProjectStatus.Delivered => "success",
            ProjectStatus.OnHold => "warning",
            ProjectStatus.Cancelled => "danger",
            _ => "secondary"
        };
    }

    public IActionResult Privacy()
    {
        return View();
    }

    [ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]
    public IActionResult Error()
    {
        return View(new ErrorViewModel { RequestId = Activity.Current?.Id ?? HttpContext.TraceIdentifier });
    }
}
