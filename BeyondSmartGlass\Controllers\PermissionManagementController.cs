using BeyondSmartGlass.Models;
using BeyondSmartGlass.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using System.Security.Claims;

namespace BeyondSmartGlass.Controllers
{
    [Authorize(Roles = "Admin")]
    public class PermissionManagementController : Controller
    {
        private readonly IPermissionService _permissionService;
        private readonly UserManager<ApplicationUser> _userManager;

        public PermissionManagementController(
            IPermissionService permissionService,
            UserManager<ApplicationUser> userManager)
        {
            _permissionService = permissionService;
            _userManager = userManager;
        }

        public async Task<IActionResult> Index()
        {
            var permissions = await _permissionService.GetAllPermissionsAsync();
            return View(permissions);
        }

        public async Task<IActionResult> Grant()
        {
            await PopulateViewBags();
            return View(new GrantPermissionViewModel());
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Grant(GrantPermissionViewModel model)
        {
            if (ModelState.IsValid)
            {
                var currentUserId = User.FindFirstValue(ClaimTypes.NameIdentifier);
                var success = await _permissionService.GrantPermissionAsync(
                    model.UserId,
                    model.AccessRight,
                    currentUserId!,
                    model.ExpiryDate,
                    model.Notes);

                if (success)
                {
                    TempData["SuccessMessage"] = "Permission granted successfully.";
                    return RedirectToAction(nameof(Index));
                }

                ModelState.AddModelError("", "Failed to grant permission.");
            }

            await PopulateViewBags();
            return View(model);
        }

        [HttpPost]
        public async Task<IActionResult> Revoke(string userId, string accessRight)
        {
            var success = await _permissionService.RevokePermissionAsync(userId, accessRight);
            
            if (success)
            {
                TempData["SuccessMessage"] = "Permission revoked successfully.";
            }
            else
            {
                TempData["ErrorMessage"] = "Failed to revoke permission.";
            }

            return RedirectToAction(nameof(Index));
        }

        public async Task<IActionResult> UserPermissions(string userId)
        {
            if (string.IsNullOrEmpty(userId))
            {
                return NotFound();
            }

            var user = await _userManager.FindByIdAsync(userId);
            if (user == null)
            {
                return NotFound();
            }

            var permissions = await _permissionService.GetUserPermissionsAsync(userId);
            
            ViewBag.User = user;
            return View(permissions);
        }

        private async Task PopulateViewBags()
        {
            // Get all users except admins
            var allUsers = _userManager.Users.ToList();
            var nonAdminUsers = new List<ApplicationUser>();

            foreach (var user in allUsers)
            {
                if (!await _userManager.IsInRoleAsync(user, "Admin"))
                {
                    nonAdminUsers.Add(user);
                }
            }

            ViewBag.Users = new SelectList(nonAdminUsers, "Id", "FullName");
            ViewBag.AccessRights = new SelectList(AccessRights.GetAllAccessRights());
        }
    }

    public class GrantPermissionViewModel
    {
        public string UserId { get; set; } = string.Empty;
        public string AccessRight { get; set; } = string.Empty;
        public DateTime? ExpiryDate { get; set; }
        public string? Notes { get; set; }
    }
}
