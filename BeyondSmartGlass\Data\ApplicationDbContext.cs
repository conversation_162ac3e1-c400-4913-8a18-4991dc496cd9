using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using BeyondSmartGlass.Models;

namespace BeyondSmartGlass.Data
{
    public class ApplicationDbContext : IdentityDbContext<ApplicationUser>
    {
        public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options)
            : base(options)
        {
        }

        // DbSets for all entities
        public DbSet<Customer> Customers { get; set; }
        public DbSet<Quotation> Quotations { get; set; }
        public DbSet<QuotationItem> QuotationItems { get; set; }
        public DbSet<Contract> Contracts { get; set; }
        public DbSet<Project> Projects { get; set; }
        public DbSet<Material> Materials { get; set; }
        public DbSet<MaterialIssue> MaterialIssues { get; set; }
        public DbSet<MaterialReturn> MaterialReturns { get; set; }
        public DbSet<StockMovement> StockMovements { get; set; }
        public DbSet<ProductionOrder> ProductionOrders { get; set; }
        public DbSet<ProductionOrderMaterial> ProductionOrderMaterials { get; set; }
        public DbSet<Installation> Installations { get; set; }
        public DbSet<InstallationTeamMember> InstallationTeamMembers { get; set; }
        public DbSet<Warranty> Warranties { get; set; }
        public DbSet<MaintenanceTicket> MaintenanceTickets { get; set; }
        public DbSet<DelegatedPermission> DelegatedPermissions { get; set; }

        protected override void OnModelCreating(ModelBuilder builder)
        {
            base.OnModelCreating(builder);

            // Configure relationships and constraints
            
            // Customer relationships
            builder.Entity<Quotation>()
                .HasOne(q => q.Customer)
                .WithMany(c => c.Quotations)
                .HasForeignKey(q => q.CustomerId)
                .OnDelete(DeleteBehavior.Restrict);

            builder.Entity<Contract>()
                .HasOne(c => c.Customer)
                .WithMany(cu => cu.Contracts)
                .HasForeignKey(c => c.CustomerId)
                .OnDelete(DeleteBehavior.Restrict);

            builder.Entity<Project>()
                .HasOne(p => p.Customer)
                .WithMany(c => c.Projects)
                .HasForeignKey(p => p.CustomerId)
                .OnDelete(DeleteBehavior.Restrict);

            // Quotation relationships
            builder.Entity<QuotationItem>()
                .HasOne(qi => qi.Quotation)
                .WithMany(q => q.Items)
                .HasForeignKey(qi => qi.QuotationId)
                .OnDelete(DeleteBehavior.Cascade);

            builder.Entity<Contract>()
                .HasOne(c => c.Quotation)
                .WithOne(q => q.Contract)
                .HasForeignKey<Contract>(c => c.QuotationId)
                .OnDelete(DeleteBehavior.SetNull);

            // Project relationships
            builder.Entity<Project>()
                .HasOne(p => p.Contract)
                .WithOne(c => c.Project)
                .HasForeignKey<Project>(p => p.ContractId)
                .OnDelete(DeleteBehavior.SetNull);

            builder.Entity<Project>()
                .HasOne(p => p.AssignedSalesRep)
                .WithMany(u => u.AssignedProjects)
                .HasForeignKey(p => p.AssignedSalesRepId)
                .OnDelete(DeleteBehavior.SetNull);

            // Production relationships
            builder.Entity<ProductionOrder>()
                .HasOne(po => po.Project)
                .WithMany(p => p.ProductionOrders)
                .HasForeignKey(po => po.ProjectId)
                .OnDelete(DeleteBehavior.Restrict);

            builder.Entity<ProductionOrder>()
                .HasOne(po => po.AssignedTo)
                .WithMany(u => u.AssignedProductionOrders)
                .HasForeignKey(po => po.AssignedToId)
                .OnDelete(DeleteBehavior.SetNull);

            // Material relationships
            builder.Entity<MaterialIssue>()
                .HasOne(mi => mi.Project)
                .WithMany(p => p.MaterialIssues)
                .HasForeignKey(mi => mi.ProjectId)
                .OnDelete(DeleteBehavior.Restrict);

            builder.Entity<MaterialIssue>()
                .HasOne(mi => mi.Material)
                .WithMany(m => m.MaterialIssues)
                .HasForeignKey(mi => mi.MaterialId)
                .OnDelete(DeleteBehavior.Restrict);

            // Installation relationships
            builder.Entity<Installation>()
                .HasOne(i => i.Project)
                .WithMany(p => p.Installations)
                .HasForeignKey(i => i.ProjectId)
                .OnDelete(DeleteBehavior.Restrict);

            builder.Entity<Installation>()
                .HasOne(i => i.AssignedTeamLead)
                .WithMany(u => u.AssignedInstallations)
                .HasForeignKey(i => i.AssignedTeamLeadId)
                .OnDelete(DeleteBehavior.SetNull);

            // Warranty relationships
            builder.Entity<Warranty>()
                .HasOne(w => w.Project)
                .WithMany(p => p.Warranties)
                .HasForeignKey(w => w.ProjectId)
                .OnDelete(DeleteBehavior.Restrict);

            builder.Entity<MaintenanceTicket>()
                .HasOne(mt => mt.Warranty)
                .WithMany(w => w.MaintenanceTickets)
                .HasForeignKey(mt => mt.WarrantyId)
                .OnDelete(DeleteBehavior.Restrict);

            // Configure unique constraints
            builder.Entity<Customer>()
                .HasIndex(c => c.Name)
                .IsUnique();

            builder.Entity<Quotation>()
                .HasIndex(q => q.QuotationNumber)
                .IsUnique();

            builder.Entity<Contract>()
                .HasIndex(c => c.ContractNumber)
                .IsUnique();

            builder.Entity<Material>()
                .HasIndex(m => m.ItemCode)
                .IsUnique();

            builder.Entity<ProductionOrder>()
                .HasIndex(po => po.OrderNumber)
                .IsUnique();

            builder.Entity<Warranty>()
                .HasIndex(w => w.WarrantyNumber)
                .IsUnique();

            builder.Entity<MaintenanceTicket>()
                .HasIndex(mt => mt.TicketNumber)
                .IsUnique();

            // DelegatedPermission relationships
            builder.Entity<DelegatedPermission>()
                .HasOne(dp => dp.User)
                .WithMany()
                .HasForeignKey(dp => dp.UserId)
                .OnDelete(DeleteBehavior.Cascade);

            builder.Entity<DelegatedPermission>()
                .HasOne(dp => dp.GrantedBy)
                .WithMany()
                .HasForeignKey(dp => dp.GrantedById)
                .OnDelete(DeleteBehavior.SetNull);

            // Unique constraint for user-access right combination
            builder.Entity<DelegatedPermission>()
                .HasIndex(dp => new { dp.UserId, dp.AccessRight })
                .IsUnique();
        }
    }
}
