using Microsoft.AspNetCore.Identity;
using System.ComponentModel.DataAnnotations;

namespace BeyondSmartGlass.Models
{
    public class ApplicationUser : IdentityUser
    {
        [Required]
        [StringLength(100)]
        public string FirstName { get; set; } = string.Empty;

        [Required]
        [StringLength(100)]
        public string LastName { get; set; } = string.Empty;

        [StringLength(100)]
        public string? City { get; set; }

        [StringLength(20)]
        public string? Phone { get; set; }

        public bool IsActive { get; set; } = true;

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        public DateTime? LastLoginAt { get; set; }

        // Navigation properties
        public virtual ICollection<Project> AssignedProjects { get; set; } = new List<Project>();
        public virtual ICollection<Installation> AssignedInstallations { get; set; } = new List<Installation>();
        public virtual ICollection<ProductionOrder> AssignedProductionOrders { get; set; } = new List<ProductionOrder>();

        public string FullName => $"{FirstName} {LastName}";
    }

    public static class UserRoles
    {
        public const string Admin = "Admin";
        public const string SalesRep = "SalesRep";
        public const string Warehouse = "Warehouse";
        public const string Production = "Production";
        public const string Installer = "Installer";
    }
}
