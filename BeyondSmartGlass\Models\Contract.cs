using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace BeyondSmartGlass.Models
{
    public class Contract
    {
        public int Id { get; set; }

        [Required]
        [StringLength(50)]
        public string ContractNumber { get; set; } = string.Empty;

        [Required]
        public int CustomerId { get; set; }

        public int? QuotationId { get; set; }

        public DateTime SigningDate { get; set; }

        public DateTime ProjectStartDate { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalCost { get; set; }

        public int DurationInDays { get; set; }

        [StringLength(500)]
        public string? ContractFilePath { get; set; }

        [StringLength(1000)]
        public string? Notes { get; set; }

        public ContractStatus Status { get; set; } = ContractStatus.Active;

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        // Navigation properties
        public virtual Customer Customer { get; set; } = null!;
        public virtual Quotation? Quotation { get; set; }
        public virtual Project? Project { get; set; }
    }

    public enum ContractStatus
    {
        Draft = 0,
        Active = 1,
        Completed = 2,
        Cancelled = 3
    }
}
