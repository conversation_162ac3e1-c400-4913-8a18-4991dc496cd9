using BeyondSmartGlass.Services;

namespace BeyondSmartGlass.Models
{
    public class DashboardViewModel
    {
        public DashboardData DashboardData { get; set; } = new();
        public List<MonthlyData> MonthlyData { get; set; } = new();
        public string SelectedPeriod { get; set; } = "monthly";
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public int SelectedYear { get; set; } = DateTime.Now.Year;
    }
}
