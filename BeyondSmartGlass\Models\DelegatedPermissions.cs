using System.ComponentModel.DataAnnotations;

namespace BeyondSmartGlass.Models
{
    public class DelegatedPermission
    {
        public int Id { get; set; }

        [Required]
        [StringLength(450)]
        public string UserId { get; set; } = string.Empty;

        [Required]
        [StringLength(100)]
        public string AccessRight { get; set; } = string.Empty;

        public DateTime GrantedAt { get; set; } = DateTime.UtcNow;

        public DateTime? ExpiryDate { get; set; }

        [StringLength(450)]
        public string? GrantedById { get; set; }

        [StringLength(500)]
        public string? Notes { get; set; }

        public bool IsActive { get; set; } = true;

        // Navigation properties
        public virtual ApplicationUser User { get; set; } = null!;
        public virtual ApplicationUser? GrantedBy { get; set; }

        // Helper property to check if permission is currently valid
        public bool IsValid => IsActive && (ExpiryDate == null || ExpiryDate > DateTime.UtcNow);
    }

    public static class AccessRights
    {
        public const string CanAccessDashboard = "CanAccessDashboard";
        public const string CanManageUsers = "CanManageUsers";
        public const string CanViewReports = "CanViewReports";
        public const string CanManageCustomers = "CanManageCustomers";
        public const string CanManageProjects = "CanManageProjects";
        public const string CanManageWarehouse = "CanManageWarehouse";
        public const string CanManageProduction = "CanManageProduction";
        public const string CanManageInstallation = "CanManageInstallation";
        public const string CanManageWarranty = "CanManageWarranty";

        public static List<string> GetAllAccessRights()
        {
            return new List<string>
            {
                CanAccessDashboard,
                CanManageUsers,
                CanViewReports,
                CanManageCustomers,
                CanManageProjects,
                CanManageWarehouse,
                CanManageProduction,
                CanManageInstallation,
                CanManageWarranty
            };
        }
    }
}
