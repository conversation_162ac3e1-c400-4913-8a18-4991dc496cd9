using System.ComponentModel.DataAnnotations;

namespace BeyondSmartGlass.Models
{
    public class GrantPermissionViewModel
    {
        [Required]
        public string UserId { get; set; } = string.Empty;
        
        [Required]
        public string AccessRight { get; set; } = string.Empty;
        
        public DateTime? ExpiryDate { get; set; }
        
        [StringLength(500)]
        public string? Notes { get; set; }
    }
}
