using System.ComponentModel.DataAnnotations;

namespace BeyondSmartGlass.Models
{
    public class Installation
    {
        public int Id { get; set; }

        [Required]
        public int ProjectId { get; set; }

        [StringLength(450)]
        public string? AssignedTeamLeadId { get; set; }

        public DateTime? ScheduledDate { get; set; }

        public DateTime? ActualStartDate { get; set; }

        public DateTime? ActualEndDate { get; set; }

        public InstallationStatus Status { get; set; } = InstallationStatus.Scheduled;

        [StringLength(1000)]
        public string? Notes { get; set; }

        [StringLength(1000)]
        public string? CompletionNotes { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        // Navigation properties
        public virtual Project Project { get; set; } = null!;
        public virtual ApplicationUser? AssignedTeamLead { get; set; }
        public virtual ICollection<InstallationTeamMember> TeamMembers { get; set; } = new List<InstallationTeamMember>();
    }

    public class InstallationTeamMember
    {
        public int Id { get; set; }

        [Required]
        public int InstallationId { get; set; }

        [Required]
        [StringLength(450)]
        public string UserId { get; set; } = string.Empty;

        [StringLength(100)]
        public string? Role { get; set; }

        public DateTime AssignedAt { get; set; } = DateTime.UtcNow;

        // Navigation properties
        public virtual Installation Installation { get; set; } = null!;
        public virtual ApplicationUser User { get; set; } = null!;
    }

    public enum InstallationStatus
    {
        Scheduled = 0,
        InProgress = 1,
        Completed = 2,
        OnHold = 3,
        Cancelled = 4
    }
}
