using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace BeyondSmartGlass.Models
{
    public class ProductionOrder
    {
        public int Id { get; set; }

        [Required]
        [StringLength(50)]
        public string OrderNumber { get; set; } = string.Empty;

        [Required]
        public int ProjectId { get; set; }

        public ProductionStatus Status { get; set; } = ProductionStatus.Preparing;

        public DateTime? StartDate { get; set; }

        public DateTime? EndDate { get; set; }

        [StringLength(450)]
        public string? AssignedToId { get; set; }

        [StringLength(1000)]
        public string? Notes { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        // Navigation properties
        public virtual Project Project { get; set; } = null!;
        public virtual ApplicationUser? AssignedTo { get; set; }
        public virtual ICollection<ProductionOrderMaterial> Materials { get; set; } = new List<ProductionOrderMaterial>();
    }

    public class ProductionOrderMaterial
    {
        public int Id { get; set; }

        [Required]
        public int ProductionOrderId { get; set; }

        [Required]
        public int MaterialId { get; set; }

        public int QuantityNeeded { get; set; }

        public int QuantityAllocated { get; set; } = 0;

        public bool IsAllocated { get; set; } = false;

        // Navigation properties
        public virtual ProductionOrder ProductionOrder { get; set; } = null!;
        public virtual Material Material { get; set; } = null!;
    }

    public enum ProductionStatus
    {
        Preparing = 0,
        InProgress = 1,
        Completed = 2,
        OnHold = 3,
        Cancelled = 4
    }
}
