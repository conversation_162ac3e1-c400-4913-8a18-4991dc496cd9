using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace BeyondSmartGlass.Models
{
    public class Project
    {
        public int Id { get; set; }

        [Required]
        [StringLength(200)]
        public string ProjectName { get; set; } = string.Empty;

        [Required]
        public int CustomerId { get; set; }

        public int? ContractId { get; set; }

        [Required]
        [StringLength(500)]
        public string Location { get; set; } = string.Empty;

        public ProjectStatus Status { get; set; } = ProjectStatus.NotStarted;

        [StringLength(450)]
        public string? AssignedSalesRepId { get; set; }

        [StringLength(1000)]
        public string? Notes { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        public DateTime? StartedAt { get; set; }

        public DateTime? CompletedAt { get; set; }

        public DateTime? DeliveredAt { get; set; }

        // Navigation properties
        public virtual Customer Customer { get; set; } = null!;
        public virtual Contract? Contract { get; set; }
        public virtual ApplicationUser? AssignedSalesRep { get; set; }
        public virtual ICollection<ProductionOrder> ProductionOrders { get; set; } = new List<ProductionOrder>();
        public virtual ICollection<Installation> Installations { get; set; } = new List<Installation>();
        public virtual ICollection<MaterialIssue> MaterialIssues { get; set; } = new List<MaterialIssue>();
        public virtual ICollection<Warranty> Warranties { get; set; } = new List<Warranty>();
    }

    public enum ProjectStatus
    {
        NotStarted = 0,
        InProgress = 1,
        WaitingMaterials = 2,
        Installed = 3,
        Delivered = 4,
        OnHold = 5,
        Cancelled = 6
    }
}
