using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace BeyondSmartGlass.Models
{
    public class Quotation
    {
        public int Id { get; set; }

        [Required]
        [StringLength(50)]
        public string QuotationNumber { get; set; } = string.Empty;

        public DateTime QuotationDate { get; set; } = DateTime.UtcNow;

        [Required]
        public int CustomerId { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalCost { get; set; }

        public QuotationStatus Status { get; set; } = QuotationStatus.Pending;

        [StringLength(1000)]
        public string? Notes { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        public DateTime? ApprovedAt { get; set; }

        public DateTime? RejectedAt { get; set; }

        [StringLength(500)]
        public string? RejectionReason { get; set; }

        // Navigation properties
        public virtual Customer Customer { get; set; } = null!;
        public virtual ICollection<QuotationItem> Items { get; set; } = new List<QuotationItem>();
        public virtual Contract? Contract { get; set; }
    }

    public class QuotationItem
    {
        public int Id { get; set; }

        [Required]
        public int QuotationId { get; set; }

        [Required]
        [StringLength(200)]
        public string ItemName { get; set; } = string.Empty;

        [StringLength(500)]
        public string? Description { get; set; }

        public int Quantity { get; set; }

        [StringLength(20)]
        public string Unit { get; set; } = "PCS";

        [Column(TypeName = "decimal(18,2)")]
        public decimal UnitPrice { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalPrice { get; set; }

        // Navigation properties
        public virtual Quotation Quotation { get; set; } = null!;
    }

    public enum QuotationStatus
    {
        Pending = 0,
        Approved = 1,
        Rejected = 2,
        Converted = 3
    }
}
