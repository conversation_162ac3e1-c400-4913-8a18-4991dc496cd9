namespace BeyondSmartGlass.Models.ViewModels
{
    public class DashboardViewModel
    {
        public DashboardKPIs KPIs { get; set; } = new DashboardKPIs();
        public List<ProjectStatusSummary> ProjectsByStatus { get; set; } = new List<ProjectStatusSummary>();
        public List<MaterialShortage> MaterialShortages { get; set; } = new List<MaterialShortage>();
        public List<PendingInstallation> PendingInstallations { get; set; } = new List<PendingInstallation>();
        public List<RecentActivity> RecentActivities { get; set; } = new List<RecentActivity>();
        public DashboardFilters Filters { get; set; } = new DashboardFilters();
    }

    public class DashboardKPIs
    {
        public int TotalProjects { get; set; }
        public int ActiveProjects { get; set; }
        public int CompletedProjects { get; set; }
        public int PendingQuotations { get; set; }
        public int ActiveContracts { get; set; }
        public int PendingInstallations { get; set; }
        public int ActiveWarranties { get; set; }
        public int OpenMaintenanceTickets { get; set; }
        public decimal TotalRevenue { get; set; }
        public decimal PendingRevenue { get; set; }
    }

    public class ProjectStatusSummary
    {
        public ProjectStatus Status { get; set; }
        public int Count { get; set; }
        public string StatusName { get; set; } = string.Empty;
        public string Color { get; set; } = string.Empty;
    }

    public class MaterialShortage
    {
        public int MaterialId { get; set; }
        public string MaterialName { get; set; } = string.Empty;
        public string ItemCode { get; set; } = string.Empty;
        public int CurrentStock { get; set; }
        public int MinimumStock { get; set; }
        public int ShortageQuantity { get; set; }
        public string Unit { get; set; } = string.Empty;
    }

    public class PendingInstallation
    {
        public int InstallationId { get; set; }
        public int ProjectId { get; set; }
        public string ProjectName { get; set; } = string.Empty;
        public string CustomerName { get; set; } = string.Empty;
        public string Location { get; set; } = string.Empty;
        public DateTime? ScheduledDate { get; set; }
        public string? AssignedTeamLead { get; set; }
        public int DaysOverdue { get; set; }
    }

    public class RecentActivity
    {
        public string Type { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public DateTime Timestamp { get; set; }
        public string UserName { get; set; } = string.Empty;
        public string Icon { get; set; } = string.Empty;
        public string Color { get; set; } = string.Empty;
    }

    public class DashboardFilters
    {
        public string? City { get; set; }
        public string? TeamId { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public ProjectStatus? Status { get; set; }
    }
}
