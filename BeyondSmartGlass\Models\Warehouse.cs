using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace BeyondSmartGlass.Models
{
    public class Material
    {
        public int Id { get; set; }

        [Required]
        [StringLength(200)]
        public string ItemName { get; set; } = string.Empty;

        [Required]
        [StringLength(50)]
        public string ItemCode { get; set; } = string.Empty;

        [StringLength(20)]
        public string Unit { get; set; } = "PCS";

        public int CurrentStock { get; set; }

        public int MinimumStock { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal UnitCost { get; set; }

        [StringLength(500)]
        public string? Description { get; set; }

        public bool IsActive { get; set; } = true;

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        // Navigation properties
        public virtual ICollection<MaterialIssue> MaterialIssues { get; set; } = new List<MaterialIssue>();
        public virtual ICollection<MaterialReturn> MaterialReturns { get; set; } = new List<MaterialReturn>();
        public virtual ICollection<StockMovement> StockMovements { get; set; } = new List<StockMovement>();
        public virtual ICollection<ProductionOrderMaterial> ProductionOrderMaterials { get; set; } = new List<ProductionOrderMaterial>();
    }

    public class MaterialIssue
    {
        public int Id { get; set; }

        [Required]
        public int ProjectId { get; set; }

        [Required]
        public int MaterialId { get; set; }

        public int QuantityIssued { get; set; }

        public DateTime IssueDate { get; set; } = DateTime.UtcNow;

        [StringLength(500)]
        public string? Notes { get; set; }

        [StringLength(450)]
        public string? IssuedById { get; set; }

        // Navigation properties
        public virtual Project Project { get; set; } = null!;
        public virtual Material Material { get; set; } = null!;
        public virtual ApplicationUser? IssuedBy { get; set; }
    }

    public class MaterialReturn
    {
        public int Id { get; set; }

        [Required]
        public int ProjectId { get; set; }

        [Required]
        public int MaterialId { get; set; }

        public int QuantityReturned { get; set; }

        public DateTime ReturnDate { get; set; } = DateTime.UtcNow;

        [StringLength(500)]
        public string? Reason { get; set; }

        [StringLength(450)]
        public string? ReturnedById { get; set; }

        // Navigation properties
        public virtual Project Project { get; set; } = null!;
        public virtual Material Material { get; set; } = null!;
        public virtual ApplicationUser? ReturnedBy { get; set; }
    }

    public class StockMovement
    {
        public int Id { get; set; }

        [Required]
        public int MaterialId { get; set; }

        public StockMovementType MovementType { get; set; }

        public int Quantity { get; set; }

        public int PreviousStock { get; set; }

        public int NewStock { get; set; }

        public DateTime MovementDate { get; set; } = DateTime.UtcNow;

        [StringLength(500)]
        public string? Reference { get; set; }

        [StringLength(500)]
        public string? Notes { get; set; }

        // Navigation properties
        public virtual Material Material { get; set; } = null!;
    }

    public enum StockMovementType
    {
        Purchase = 0,
        Issue = 1,
        Return = 2,
        Adjustment = 3,
        Transfer = 4
    }
}
