using System.ComponentModel.DataAnnotations;

namespace BeyondSmartGlass.Models
{
    public class Warranty
    {
        public int Id { get; set; }

        [Required]
        [StringLength(50)]
        public string WarrantyNumber { get; set; } = string.Empty;

        [Required]
        public int ProjectId { get; set; }

        public DateTime StartDate { get; set; }

        public DateTime EndDate { get; set; }

        public WarrantyStatus Status { get; set; } = WarrantyStatus.Active;

        [StringLength(1000)]
        public string? Terms { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        // Navigation properties
        public virtual Project Project { get; set; } = null!;
        public virtual ICollection<MaintenanceTicket> MaintenanceTickets { get; set; } = new List<MaintenanceTicket>();
    }

    public class MaintenanceTicket
    {
        public int Id { get; set; }

        [Required]
        [StringLength(50)]
        public string TicketNumber { get; set; } = string.Empty;

        [Required]
        public int WarrantyId { get; set; }

        [Required]
        [StringLength(200)]
        public string Title { get; set; } = string.Empty;

        [Required]
        [StringLength(1000)]
        public string Description { get; set; } = string.Empty;

        public MaintenanceTicketStatus Status { get; set; } = MaintenanceTicketStatus.Open;

        public MaintenancePriority Priority { get; set; } = MaintenancePriority.Medium;

        [StringLength(450)]
        public string? AssignedToId { get; set; }

        public DateTime? ScheduledDate { get; set; }

        public DateTime? CompletedDate { get; set; }

        [StringLength(1000)]
        public string? Resolution { get; set; }

        [StringLength(1000)]
        public string? Notes { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        // Navigation properties
        public virtual Warranty Warranty { get; set; } = null!;
        public virtual ApplicationUser? AssignedTo { get; set; }
    }

    public enum WarrantyStatus
    {
        Active = 0,
        Expired = 1,
        Voided = 2
    }

    public enum MaintenanceTicketStatus
    {
        Open = 0,
        InProgress = 1,
        Closed = 2,
        Cancelled = 3
    }

    public enum MaintenancePriority
    {
        Low = 0,
        Medium = 1,
        High = 2,
        Critical = 3
    }
}
