<?xml version="1.0" encoding="utf-8"?>
<root>
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="AppName" xml:space="preserve">
    <value>بيوند سمارت جلاس</value>
  </data>
  <data name="Dashboard" xml:space="preserve">
    <value>لوحة التحكم</value>
  </data>
  <data name="Customers" xml:space="preserve">
    <value>العملاء</value>
  </data>
  <data name="Quotations" xml:space="preserve">
    <value>عروض الأسعار</value>
  </data>
  <data name="Contracts" xml:space="preserve">
    <value>العقود</value>
  </data>
  <data name="Projects" xml:space="preserve">
    <value>المشاريع</value>
  </data>
  <data name="Warehouse" xml:space="preserve">
    <value>المستودع</value>
  </data>
  <data name="Production" xml:space="preserve">
    <value>الإنتاج</value>
  </data>
  <data name="Installation" xml:space="preserve">
    <value>التركيب</value>
  </data>
  <data name="Warranty" xml:space="preserve">
    <value>الضمان</value>
  </data>
  <data name="Login" xml:space="preserve">
    <value>تسجيل الدخول</value>
  </data>
  <data name="Logout" xml:space="preserve">
    <value>تسجيل الخروج</value>
  </data>
  <data name="Name" xml:space="preserve">
    <value>الاسم</value>
  </data>
  <data name="Email" xml:space="preserve">
    <value>البريد الإلكتروني</value>
  </data>
  <data name="Phone" xml:space="preserve">
    <value>الهاتف</value>
  </data>
  <data name="City" xml:space="preserve">
    <value>المدينة</value>
  </data>
  <data name="Address" xml:space="preserve">
    <value>العنوان</value>
  </data>
  <data name="Save" xml:space="preserve">
    <value>حفظ</value>
  </data>
  <data name="Cancel" xml:space="preserve">
    <value>إلغاء</value>
  </data>
  <data name="Edit" xml:space="preserve">
    <value>تعديل</value>
  </data>
  <data name="Delete" xml:space="preserve">
    <value>حذف</value>
  </data>
  <data name="Add" xml:space="preserve">
    <value>إضافة</value>
  </data>
  <data name="Search" xml:space="preserve">
    <value>بحث</value>
  </data>
  <data name="Status" xml:space="preserve">
    <value>الحالة</value>
  </data>
  <data name="Date" xml:space="preserve">
    <value>التاريخ</value>
  </data>
  <data name="Total" xml:space="preserve">
    <value>المجموع</value>
  </data>
  <data name="Actions" xml:space="preserve">
    <value>الإجراءات</value>
  </data>
</root>
