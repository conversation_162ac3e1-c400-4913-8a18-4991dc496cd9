<?xml version="1.0" encoding="utf-8"?>
<root>
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="AppName" xml:space="preserve">
    <value>بيوند سمارت جلاس</value>
  </data>
  <data name="Dashboard" xml:space="preserve">
    <value>لوحة التحكم</value>
  </data>
  <data name="Customers" xml:space="preserve">
    <value>العملاء</value>
  </data>
  <data name="Quotations" xml:space="preserve">
    <value>عروض الأسعار</value>
  </data>
  <data name="Contracts" xml:space="preserve">
    <value>العقود</value>
  </data>
  <data name="Projects" xml:space="preserve">
    <value>المشاريع</value>
  </data>
  <data name="Warehouse" xml:space="preserve">
    <value>المستودع</value>
  </data>
  <data name="Production" xml:space="preserve">
    <value>الإنتاج</value>
  </data>
  <data name="Installation" xml:space="preserve">
    <value>التركيب</value>
  </data>
  <data name="Warranty" xml:space="preserve">
    <value>الضمان</value>
  </data>
  <data name="Login" xml:space="preserve">
    <value>تسجيل الدخول</value>
  </data>
  <data name="Logout" xml:space="preserve">
    <value>تسجيل الخروج</value>
  </data>
  <data name="Name" xml:space="preserve">
    <value>الاسم</value>
  </data>
  <data name="Email" xml:space="preserve">
    <value>البريد الإلكتروني</value>
  </data>
  <data name="Phone" xml:space="preserve">
    <value>الهاتف</value>
  </data>
  <data name="City" xml:space="preserve">
    <value>المدينة</value>
  </data>
  <data name="Address" xml:space="preserve">
    <value>العنوان</value>
  </data>
  <data name="Save" xml:space="preserve">
    <value>حفظ</value>
  </data>
  <data name="Cancel" xml:space="preserve">
    <value>إلغاء</value>
  </data>
  <data name="Edit" xml:space="preserve">
    <value>تعديل</value>
  </data>
  <data name="Delete" xml:space="preserve">
    <value>حذف</value>
  </data>
  <data name="Add" xml:space="preserve">
    <value>إضافة</value>
  </data>
  <data name="Search" xml:space="preserve">
    <value>بحث</value>
  </data>
  <data name="Status" xml:space="preserve">
    <value>الحالة</value>
  </data>
  <data name="Date" xml:space="preserve">
    <value>التاريخ</value>
  </data>
  <data name="Total" xml:space="preserve">
    <value>المجموع</value>
  </data>
  <data name="Actions" xml:space="preserve">
    <value>الإجراءات</value>
  </data>
  <data name="Operations" xml:space="preserve">
    <value>العمليات</value>
  </data>
  <data name="Profile" xml:space="preserve">
    <value>الملف الشخصي</value>
  </data>
  <data name="Password" xml:space="preserve">
    <value>كلمة المرور</value>
  </data>
  <data name="RememberMe" xml:space="preserve">
    <value>تذكرني؟</value>
  </data>
  <data name="CreateNewAccount" xml:space="preserve">
    <value>إنشاء حساب جديد</value>
  </data>
  <data name="Register" xml:space="preserve">
    <value>تسجيل</value>
  </data>
  <data name="FirstName" xml:space="preserve">
    <value>الاسم الأول</value>
  </data>
  <data name="LastName" xml:space="preserve">
    <value>اسم العائلة</value>
  </data>
  <data name="ConfirmPassword" xml:space="preserve">
    <value>تأكيد كلمة المرور</value>
  </data>
  <data name="AlreadyHaveAccount" xml:space="preserve">
    <value>لديك حساب بالفعل؟</value>
  </data>
  <data name="FooterDescription" xml:space="preserve">
    <value>حلول تصنيع وتركيب الزجاج الذكي</value>
  </data>
  <data name="AllRightsReserved" xml:space="preserve">
    <value>جميع الحقوق محفوظة.</value>
  </data>
  <data name="Welcome" xml:space="preserve">
    <value>مرحباً</value>
  </data>
  <data name="Description" xml:space="preserve">
    <value>الوصف</value>
  </data>
  <data name="Location" xml:space="preserve">
    <value>الموقع</value>
  </data>
  <data name="Quantity" xml:space="preserve">
    <value>الكمية</value>
  </data>
  <data name="Price" xml:space="preserve">
    <value>السعر</value>
  </data>
  <data name="Unit" xml:space="preserve">
    <value>الوحدة</value>
  </data>
  <data name="Notes" xml:space="preserve">
    <value>ملاحظات</value>
  </data>
  <data name="CreatedAt" xml:space="preserve">
    <value>تاريخ الإنشاء</value>
  </data>
  <data name="UpdatedAt" xml:space="preserve">
    <value>تاريخ التحديث</value>
  </data>
  <data name="Customer" xml:space="preserve">
    <value>عميل</value>
  </data>
  <data name="AddCustomer" xml:space="preserve">
    <value>إضافة عميل</value>
  </data>
  <data name="EditCustomer" xml:space="preserve">
    <value>تعديل العميل</value>
  </data>
  <data name="CustomerDetails" xml:space="preserve">
    <value>تفاصيل العميل</value>
  </data>
  <data name="SearchCustomers" xml:space="preserve">
    <value>البحث عن العملاء...</value>
  </data>
  <data name="AllCities" xml:space="preserve">
    <value>جميع المدن</value>
  </data>
  <data name="Clear" xml:space="preserve">
    <value>مسح</value>
  </data>
  <data name="NoCustomersFound" xml:space="preserve">
    <value>لم يتم العثور على عملاء</value>
  </data>
  <data name="NoCustomersMessage" xml:space="preserve">
    <value>ابدأ بإضافة أول عميل لك في النظام.</value>
  </data>
  <data name="AddFirstCustomer" xml:space="preserve">
    <value>إضافة أول عميل</value>
  </data>
  <data name="Back" xml:space="preserve">
    <value>رجوع</value>
  </data>
  <data name="Update" xml:space="preserve">
    <value>تحديث</value>
  </data>
  <data name="Active" xml:space="preserve">
    <value>نشط</value>
  </data>
  <data name="Inactive" xml:space="preserve">
    <value>غير نشط</value>
  </data>
  <data name="CreateQuotation" xml:space="preserve">
    <value>إنشاء عرض سعر</value>
  </data>
  <data name="Summary" xml:space="preserve">
    <value>ملخص</value>
  </data>
  <data name="RecentActivity" xml:space="preserve">
    <value>النشاط الأخير</value>
  </data>
  <data name="QuotationCreated" xml:space="preserve">
    <value>تم إنشاء عرض سعر</value>
  </data>
  <data name="ContractSigned" xml:space="preserve">
    <value>تم توقيع العقد</value>
  </data>
  <data name="NoRecentActivity" xml:space="preserve">
    <value>لا يوجد نشاط حديث</value>
  </data>
  <data name="Details" xml:space="preserve">
    <value>التفاصيل</value>
  </data>
  <data name="WelcomeMessage" xml:space="preserve">
    <value>حل شامل لتخطيط موارد المؤسسات لإدارة تصنيع وتركيب الزجاج الذكي مع دعم كامل ثنائي اللغة.</value>
  </data>
  <data name="WhyChooseUs" xml:space="preserve">
    <value>لماذا تختار نظام بيوند سمارت جلاس؟</value>
  </data>
  <data name="WhyChooseUsDescription" xml:space="preserve">
    <value>نظام تخطيط الموارد الشامل مصمم خصيصاً لمصنعي ومركبي الزجاج الذكي.</value>
  </data>
  <data name="CustomerManagement" xml:space="preserve">
    <value>إدارة العملاء</value>
  </data>
  <data name="CustomerManagementDesc" xml:space="preserve">
    <value>إدارة معلومات العملاء وتتبع العلاقات والحفاظ على تاريخ التواصل.</value>
  </data>
  <data name="ProjectTracking" xml:space="preserve">
    <value>تتبع المشاريع</value>
  </data>
  <data name="ProjectTrackingDesc" xml:space="preserve">
    <value>تتبع المشاريع من عرض الأسعار إلى التسليم مع تحديثات الحالة في الوقت الفعلي.</value>
  </data>
  <data name="InventoryManagement" xml:space="preserve">
    <value>إدارة المخزون</value>
  </data>
  <data name="InventoryManagementDesc" xml:space="preserve">
    <value>مراقبة مستويات المخزون وتتبع استخدام المواد وإدارة عمليات المستودع.</value>
  </data>
  <data name="Features" xml:space="preserve">
    <value>المميزات</value>
  </data>
  <data name="BilingualSupport" xml:space="preserve">
    <value>دعم كامل للعربية والإنجليزية</value>
  </data>
  <data name="RoleBasedAccess" xml:space="preserve">
    <value>التحكم في الوصول حسب الدور</value>
  </data>
  <data name="RealTimeTracking" xml:space="preserve">
    <value>تتبع المشاريع في الوقت الفعلي</value>
  </data>
  <data name="ComprehensiveReporting" xml:space="preserve">
    <value>تقارير شاملة</value>
  </data>
  <data name="WarrantyManagement" xml:space="preserve">
    <value>إدارة الضمان والصيانة</value>
  </data>
  <data name="GetStarted" xml:space="preserve">
    <value>ابدأ اليوم</value>
  </data>
  <data name="GetStartedDesc" xml:space="preserve">
    <value>انضم إلى آلاف المختصين في الزجاج الذكي الذين يثقون في نظامنا لإدارة عمليات أعمالهم.</value>
  </data>
  <data name="StartNow" xml:space="preserve">
    <value>ابدأ الآن</value>
  </data>
  <data name="TotalProjects" xml:space="preserve">
    <value>إجمالي المشاريع</value>
  </data>
  <data name="ActiveProjects" xml:space="preserve">
    <value>المشاريع النشطة</value>
  </data>
  <data name="PendingQuotations" xml:space="preserve">
    <value>عروض الأسعار المعلقة</value>
  </data>
  <data name="PendingInstallations" xml:space="preserve">
    <value>التركيبات المعلقة</value>
  </data>
  <data name="TotalRevenue" xml:space="preserve">
    <value>إجمالي الإيرادات</value>
  </data>
  <data name="PendingRevenue" xml:space="preserve">
    <value>الإيرادات المعلقة</value>
  </data>
  <data name="ProjectsByStatus" xml:space="preserve">
    <value>المشاريع حسب الحالة</value>
  </data>
  <data name="MaterialShortages" xml:space="preserve">
    <value>نقص المواد</value>
  </data>
  <data name="Material" xml:space="preserve">
    <value>المادة</value>
  </data>
  <data name="Current" xml:space="preserve">
    <value>الحالي</value>
  </data>
  <data name="Minimum" xml:space="preserve">
    <value>الحد الأدنى</value>
  </data>
  <data name="Shortage" xml:space="preserve">
    <value>النقص</value>
  </data>
  <data name="NoProjectData" xml:space="preserve">
    <value>لا توجد بيانات مشاريع متاحة</value>
  </data>
  <data name="NoMaterialShortages" xml:space="preserve">
    <value>جميع المواد متوفرة بكمية كافية</value>
  </data>
</root>
