<?xml version="1.0" encoding="utf-8"?>
<root>
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="AppName" xml:space="preserve">
    <value>Beyond Smart Glass</value>
  </data>
  <data name="Dashboard" xml:space="preserve">
    <value>Dashboard</value>
  </data>
  <data name="Customers" xml:space="preserve">
    <value>Customers</value>
  </data>
  <data name="Quotations" xml:space="preserve">
    <value>Quotations</value>
  </data>
  <data name="Contracts" xml:space="preserve">
    <value>Contracts</value>
  </data>
  <data name="Projects" xml:space="preserve">
    <value>Projects</value>
  </data>
  <data name="Warehouse" xml:space="preserve">
    <value>Warehouse</value>
  </data>
  <data name="Production" xml:space="preserve">
    <value>Production</value>
  </data>
  <data name="Installation" xml:space="preserve">
    <value>Installation</value>
  </data>
  <data name="Warranty" xml:space="preserve">
    <value>Warranty</value>
  </data>
  <data name="Login" xml:space="preserve">
    <value>Login</value>
  </data>
  <data name="Logout" xml:space="preserve">
    <value>Logout</value>
  </data>
  <data name="Name" xml:space="preserve">
    <value>Name</value>
  </data>
  <data name="Email" xml:space="preserve">
    <value>Email</value>
  </data>
  <data name="Phone" xml:space="preserve">
    <value>Phone</value>
  </data>
  <data name="City" xml:space="preserve">
    <value>City</value>
  </data>
  <data name="Address" xml:space="preserve">
    <value>Address</value>
  </data>
  <data name="Save" xml:space="preserve">
    <value>Save</value>
  </data>
  <data name="Cancel" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="Edit" xml:space="preserve">
    <value>Edit</value>
  </data>
  <data name="Delete" xml:space="preserve">
    <value>Delete</value>
  </data>
  <data name="Add" xml:space="preserve">
    <value>Add</value>
  </data>
  <data name="Search" xml:space="preserve">
    <value>Search</value>
  </data>
  <data name="Status" xml:space="preserve">
    <value>Status</value>
  </data>
  <data name="Date" xml:space="preserve">
    <value>Date</value>
  </data>
  <data name="Total" xml:space="preserve">
    <value>Total</value>
  </data>
  <data name="Actions" xml:space="preserve">
    <value>Actions</value>
  </data>
  <data name="Operations" xml:space="preserve">
    <value>Operations</value>
  </data>
  <data name="Profile" xml:space="preserve">
    <value>Profile</value>
  </data>
  <data name="Password" xml:space="preserve">
    <value>Password</value>
  </data>
  <data name="RememberMe" xml:space="preserve">
    <value>Remember me?</value>
  </data>
  <data name="CreateNewAccount" xml:space="preserve">
    <value>Create new account</value>
  </data>
  <data name="Register" xml:space="preserve">
    <value>Register</value>
  </data>
  <data name="FirstName" xml:space="preserve">
    <value>First Name</value>
  </data>
  <data name="LastName" xml:space="preserve">
    <value>Last Name</value>
  </data>
  <data name="ConfirmPassword" xml:space="preserve">
    <value>Confirm Password</value>
  </data>
  <data name="AlreadyHaveAccount" xml:space="preserve">
    <value>Already have an account?</value>
  </data>
  <data name="FooterDescription" xml:space="preserve">
    <value>Smart Glass Manufacturing and Installation Solutions</value>
  </data>
  <data name="AllRightsReserved" xml:space="preserve">
    <value>All rights reserved.</value>
  </data>
  <data name="Welcome" xml:space="preserve">
    <value>Welcome</value>
  </data>
  <data name="Description" xml:space="preserve">
    <value>Description</value>
  </data>
  <data name="Location" xml:space="preserve">
    <value>Location</value>
  </data>
  <data name="Quantity" xml:space="preserve">
    <value>Quantity</value>
  </data>
  <data name="Price" xml:space="preserve">
    <value>Price</value>
  </data>
  <data name="Unit" xml:space="preserve">
    <value>Unit</value>
  </data>
  <data name="Notes" xml:space="preserve">
    <value>Notes</value>
  </data>
  <data name="CreatedAt" xml:space="preserve">
    <value>Created At</value>
  </data>
  <data name="UpdatedAt" xml:space="preserve">
    <value>Updated At</value>
  </data>
  <data name="Customer" xml:space="preserve">
    <value>Customer</value>
  </data>
  <data name="AddCustomer" xml:space="preserve">
    <value>Add Customer</value>
  </data>
  <data name="EditCustomer" xml:space="preserve">
    <value>Edit Customer</value>
  </data>
  <data name="CustomerDetails" xml:space="preserve">
    <value>Customer Details</value>
  </data>
  <data name="SearchCustomers" xml:space="preserve">
    <value>Search customers...</value>
  </data>
  <data name="AllCities" xml:space="preserve">
    <value>All Cities</value>
  </data>
  <data name="Clear" xml:space="preserve">
    <value>Clear</value>
  </data>
  <data name="NoCustomersFound" xml:space="preserve">
    <value>No customers found</value>
  </data>
  <data name="NoCustomersMessage" xml:space="preserve">
    <value>Start by adding your first customer to the system.</value>
  </data>
  <data name="AddFirstCustomer" xml:space="preserve">
    <value>Add First Customer</value>
  </data>
  <data name="Back" xml:space="preserve">
    <value>Back</value>
  </data>
  <data name="Update" xml:space="preserve">
    <value>Update</value>
  </data>
  <data name="Active" xml:space="preserve">
    <value>Active</value>
  </data>
  <data name="Inactive" xml:space="preserve">
    <value>Inactive</value>
  </data>
  <data name="CreateQuotation" xml:space="preserve">
    <value>Create Quotation</value>
  </data>
  <data name="Summary" xml:space="preserve">
    <value>Summary</value>
  </data>
  <data name="RecentActivity" xml:space="preserve">
    <value>Recent Activity</value>
  </data>
  <data name="QuotationCreated" xml:space="preserve">
    <value>Quotation Created</value>
  </data>
  <data name="ContractSigned" xml:space="preserve">
    <value>Contract Signed</value>
  </data>
  <data name="NoRecentActivity" xml:space="preserve">
    <value>No recent activity</value>
  </data>
  <data name="Details" xml:space="preserve">
    <value>Details</value>
  </data>
  <data name="WelcomeMessage" xml:space="preserve">
    <value>Comprehensive ERP solution for smart glass manufacturing and installation management with full bilingual support.</value>
  </data>
  <data name="WhyChooseUs" xml:space="preserve">
    <value>Why Choose Beyond Smart Glass ERP?</value>
  </data>
  <data name="WhyChooseUsDescription" xml:space="preserve">
    <value>Our comprehensive ERP system is designed specifically for smart glass manufacturers and installers.</value>
  </data>
  <data name="CustomerManagement" xml:space="preserve">
    <value>Customer Management</value>
  </data>
  <data name="CustomerManagementDesc" xml:space="preserve">
    <value>Manage customer information, track relationships, and maintain communication history.</value>
  </data>
  <data name="ProjectTracking" xml:space="preserve">
    <value>Project Tracking</value>
  </data>
  <data name="ProjectTrackingDesc" xml:space="preserve">
    <value>Track projects from quotation to delivery with real-time status updates.</value>
  </data>
  <data name="InventoryManagement" xml:space="preserve">
    <value>Inventory Management</value>
  </data>
  <data name="InventoryManagementDesc" xml:space="preserve">
    <value>Monitor stock levels, track material usage, and manage warehouse operations.</value>
  </data>
  <data name="Features" xml:space="preserve">
    <value>Features</value>
  </data>
  <data name="BilingualSupport" xml:space="preserve">
    <value>Full Arabic &amp; English Support</value>
  </data>
  <data name="RoleBasedAccess" xml:space="preserve">
    <value>Role-based Access Control</value>
  </data>
  <data name="RealTimeTracking" xml:space="preserve">
    <value>Real-time Project Tracking</value>
  </data>
  <data name="ComprehensiveReporting" xml:space="preserve">
    <value>Comprehensive Reporting</value>
  </data>
  <data name="WarrantyManagement" xml:space="preserve">
    <value>Warranty &amp; Maintenance Management</value>
  </data>
  <data name="GetStarted" xml:space="preserve">
    <value>Get Started Today</value>
  </data>
  <data name="GetStartedDesc" xml:space="preserve">
    <value>Join thousands of smart glass professionals who trust our ERP system to manage their business operations.</value>
  </data>
  <data name="StartNow" xml:space="preserve">
    <value>Start Now</value>
  </data>
  <data name="TotalProjects" xml:space="preserve">
    <value>Total Projects</value>
  </data>
  <data name="ActiveProjects" xml:space="preserve">
    <value>Active Projects</value>
  </data>
  <data name="PendingQuotations" xml:space="preserve">
    <value>Pending Quotations</value>
  </data>
  <data name="PendingInstallations" xml:space="preserve">
    <value>Pending Installations</value>
  </data>
  <data name="TotalRevenue" xml:space="preserve">
    <value>Total Revenue</value>
  </data>
  <data name="PendingRevenue" xml:space="preserve">
    <value>Pending Revenue</value>
  </data>
  <data name="ProjectsByStatus" xml:space="preserve">
    <value>Projects by Status</value>
  </data>
  <data name="MaterialShortages" xml:space="preserve">
    <value>Material Shortages</value>
  </data>
  <data name="Material" xml:space="preserve">
    <value>Material</value>
  </data>
  <data name="Current" xml:space="preserve">
    <value>Current</value>
  </data>
  <data name="Minimum" xml:space="preserve">
    <value>Minimum</value>
  </data>
  <data name="Shortage" xml:space="preserve">
    <value>Shortage</value>
  </data>
  <data name="NoProjectData" xml:space="preserve">
    <value>No project data available</value>
  </data>
  <data name="NoMaterialShortages" xml:space="preserve">
    <value>All materials are well stocked</value>
  </data>
</root>
