using BeyondSmartGlass.Data;
using BeyondSmartGlass.Models;
using Microsoft.EntityFrameworkCore;

namespace BeyondSmartGlass.Services
{
    public interface IDashboardService
    {
        Task<DashboardData> GetDashboardDataAsync(DateTime startDate, DateTime endDate);
        Task<List<MonthlyData>> GetMonthlyDataAsync(int year);
        Task<List<ChartData>> GetContractStatusDataAsync(DateTime startDate, DateTime endDate);
        Task<List<ChartData>> GetProjectStatusDataAsync(DateTime startDate, DateTime endDate);
        Task<List<ChartData>> GetMaintenanceStatusDataAsync(DateTime startDate, DateTime endDate);
    }

    public class DashboardService : IDashboardService
    {
        private readonly ApplicationDbContext _context;

        public DashboardService(ApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<DashboardData> GetDashboardDataAsync(DateTime startDate, DateTime endDate)
        {
            var data = new DashboardData();

            // Total contracts in period
            data.TotalContracts = await _context.Contracts
                .Where(c => c.SigningDate >= startDate && c.SigningDate <= endDate)
                .CountAsync();

            // Total quotations in period
            data.TotalQuotations = await _context.Quotations
                .Where(q => q.CreatedAt >= startDate && q.CreatedAt <= endDate)
                .CountAsync();

            // Open maintenance requests
            data.OpenMaintenanceRequests = await _context.MaintenanceTickets
                .Where(mt => mt.Status == MaintenanceTicketStatus.Open || mt.Status == MaintenanceTicketStatus.InProgress)
                .CountAsync();

            // Closed maintenance requests in period
            data.ClosedMaintenanceRequests = await _context.MaintenanceTickets
                .Where(mt => mt.Status == MaintenanceTicketStatus.Closed &&
                           mt.UpdatedAt >= startDate && mt.UpdatedAt <= endDate)
                .CountAsync();

            // Active projects
            data.ActiveProjects = await _context.Projects
                .Where(p => p.Status == ProjectStatus.InProgress || p.Status == ProjectStatus.NotStarted)
                .CountAsync();

            // Closed projects in period
            data.ClosedProjects = await _context.Projects
                .Where(p => p.Status == ProjectStatus.Delivered &&
                          p.UpdatedAt >= startDate && p.UpdatedAt <= endDate)
                .CountAsync();

            // Total sales amount in period
            data.TotalSalesAmount = await _context.Contracts
                .Where(c => c.SigningDate >= startDate && c.SigningDate <= endDate)
                .SumAsync(c => c.TotalCost);

            return data;
        }

        public async Task<List<MonthlyData>> GetMonthlyDataAsync(int year)
        {
            var monthlyData = new List<MonthlyData>();

            for (int month = 1; month <= 12; month++)
            {
                var startDate = new DateTime(year, month, 1);
                var endDate = startDate.AddMonths(1).AddDays(-1);

                var data = new MonthlyData
                {
                    Month = month,
                    MonthName = startDate.ToString("MMMM"),
                    Contracts = await _context.Contracts
                        .Where(c => c.SigningDate >= startDate && c.SigningDate <= endDate)
                        .CountAsync(),
                    Quotations = await _context.Quotations
                        .Where(q => q.CreatedAt >= startDate && q.CreatedAt <= endDate)
                        .CountAsync(),
                    SalesAmount = await _context.Contracts
                        .Where(c => c.SigningDate >= startDate && c.SigningDate <= endDate)
                        .SumAsync(c => c.TotalCost),
                    CompletedProjects = await _context.Projects
                        .Where(p => p.Status == ProjectStatus.Delivered &&
                                  p.UpdatedAt >= startDate && p.UpdatedAt <= endDate)
                        .CountAsync()
                };

                monthlyData.Add(data);
            }

            return monthlyData;
        }

        public async Task<List<ChartData>> GetContractStatusDataAsync(DateTime startDate, DateTime endDate)
        {
            var contracts = await _context.Contracts
                .Where(c => c.SigningDate >= startDate && c.SigningDate <= endDate)
                .GroupBy(c => c.Status)
                .Select(g => new ChartData
                {
                    Label = g.Key.ToString(),
                    Value = g.Count(),
                    Percentage = 0 // Will be calculated after getting total
                })
                .ToListAsync();

            var total = contracts.Sum(c => c.Value);
            foreach (var item in contracts)
            {
                item.Percentage = total > 0 ? (double)item.Value / total * 100 : 0;
            }

            return contracts;
        }

        public async Task<List<ChartData>> GetProjectStatusDataAsync(DateTime startDate, DateTime endDate)
        {
            var projects = await _context.Projects
                .Where(p => p.CreatedAt >= startDate && p.CreatedAt <= endDate)
                .GroupBy(p => p.Status)
                .Select(g => new ChartData
                {
                    Label = g.Key.ToString(),
                    Value = g.Count(),
                    Percentage = 0
                })
                .ToListAsync();

            var total = projects.Sum(p => p.Value);
            foreach (var item in projects)
            {
                item.Percentage = total > 0 ? (double)item.Value / total * 100 : 0;
            }

            return projects;
        }

        public async Task<List<ChartData>> GetMaintenanceStatusDataAsync(DateTime startDate, DateTime endDate)
        {
            var maintenance = await _context.MaintenanceTickets
                .Where(mt => mt.CreatedAt >= startDate && mt.CreatedAt <= endDate)
                .GroupBy(mt => mt.Status)
                .Select(g => new ChartData
                {
                    Label = g.Key.ToString(),
                    Value = g.Count(),
                    Percentage = 0
                })
                .ToListAsync();

            var total = maintenance.Sum(m => m.Value);
            foreach (var item in maintenance)
            {
                item.Percentage = total > 0 ? (double)item.Value / total * 100 : 0;
            }

            return maintenance;
        }
    }

    public class DashboardData
    {
        public int TotalContracts { get; set; }
        public int TotalQuotations { get; set; }
        public int OpenMaintenanceRequests { get; set; }
        public int ClosedMaintenanceRequests { get; set; }
        public int ActiveProjects { get; set; }
        public int ClosedProjects { get; set; }
        public decimal TotalSalesAmount { get; set; }
    }

    public class MonthlyData
    {
        public int Month { get; set; }
        public string MonthName { get; set; } = string.Empty;
        public int Contracts { get; set; }
        public int Quotations { get; set; }
        public decimal SalesAmount { get; set; }
        public int CompletedProjects { get; set; }
    }

    public class ChartData
    {
        public string Label { get; set; } = string.Empty;
        public int Value { get; set; }
        public double Percentage { get; set; }
    }
}
