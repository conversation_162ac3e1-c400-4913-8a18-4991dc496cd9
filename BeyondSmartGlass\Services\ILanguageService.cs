using System.Globalization;

namespace BeyondSmartGlass.Services
{
    public interface ILanguageService
    {
        CultureInfo GetCurrentCulture();
        bool IsRightToLeft();
        string GetCurrentLanguageCode();
        string GetCurrentLanguageName();
        List<LanguageInfo> GetSupportedLanguages();
    }

    public class LanguageInfo
    {
        public string Code { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string NativeName { get; set; } = string.Empty;
        public bool IsRtl { get; set; }
    }
}
