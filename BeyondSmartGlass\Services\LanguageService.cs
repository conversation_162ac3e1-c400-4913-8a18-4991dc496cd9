using System.Globalization;

namespace BeyondSmartGlass.Services
{
    public class LanguageService : ILanguageService
    {
        private readonly List<LanguageInfo> _supportedLanguages = new()
        {
            new LanguageInfo { Code = "en-US", Name = "English", NativeName = "English", IsRtl = false },
            new LanguageInfo { Code = "ar-SA", Name = "Arabic", NativeName = "العربية", IsRtl = true }
        };

        public CultureInfo GetCurrentCulture()
        {
            return CultureInfo.CurrentCulture;
        }

        public bool IsRightToLeft()
        {
            return GetCurrentCulture().TextInfo.IsRightToLeft;
        }

        public string GetCurrentLanguageCode()
        {
            return GetCurrentCulture().Name;
        }

        public string GetCurrentLanguageName()
        {
            var currentCode = GetCurrentLanguageCode();
            var language = _supportedLanguages.FirstOrDefault(l => l.Code == currentCode);
            return language?.NativeName ?? "English";
        }

        public List<LanguageInfo> GetSupportedLanguages()
        {
            return _supportedLanguages;
        }
    }
}
