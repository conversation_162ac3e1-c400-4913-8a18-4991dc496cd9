using BeyondSmartGlass.Data;
using BeyondSmartGlass.Models;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;

namespace BeyondSmartGlass.Services
{
    public interface IPermissionService
    {
        Task<bool> HasPermissionAsync(string userId, string accessRight);
        Task<bool> IsAdminAsync(string userId);
        Task<List<DelegatedPermission>> GetUserPermissionsAsync(string userId);
        Task<bool> GrantPermissionAsync(string userId, string accessRight, string grantedById, DateTime? expiryDate = null, string? notes = null);
        Task<bool> RevokePermissionAsync(string userId, string accessRight);
        Task<List<DelegatedPermission>> GetAllPermissionsAsync();
        Task<bool> UpdatePermissionAsync(int permissionId, DateTime? newExpiryDate, string? notes = null);
    }

    public class PermissionService : IPermissionService
    {
        private readonly ApplicationDbContext _context;
        private readonly UserManager<ApplicationUser> _userManager;

        public PermissionService(ApplicationDbContext context, UserManager<ApplicationUser> userManager)
        {
            _context = context;
            _userManager = userManager;
        }

        public async Task<bool> HasPermissionAsync(string userId, string accessRight)
        {
            // Check if user is admin first
            if (await IsAdminAsync(userId))
                return true;

            // Check delegated permissions
            var permission = await _context.DelegatedPermissions
                .FirstOrDefaultAsync(dp => dp.UserId == userId && 
                                          dp.AccessRight == accessRight && 
                                          dp.IsActive &&
                                          (dp.ExpiryDate == null || dp.ExpiryDate > DateTime.UtcNow));

            return permission != null;
        }

        public async Task<bool> IsAdminAsync(string userId)
        {
            var user = await _userManager.FindByIdAsync(userId);
            if (user == null) return false;

            return await _userManager.IsInRoleAsync(user, "Admin");
        }

        public async Task<List<DelegatedPermission>> GetUserPermissionsAsync(string userId)
        {
            return await _context.DelegatedPermissions
                .Include(dp => dp.User)
                .Include(dp => dp.GrantedBy)
                .Where(dp => dp.UserId == userId)
                .OrderByDescending(dp => dp.GrantedAt)
                .ToListAsync();
        }

        public async Task<bool> GrantPermissionAsync(string userId, string accessRight, string grantedById, DateTime? expiryDate = null, string? notes = null)
        {
            try
            {
                // Check if permission already exists
                var existingPermission = await _context.DelegatedPermissions
                    .FirstOrDefaultAsync(dp => dp.UserId == userId && dp.AccessRight == accessRight);

                if (existingPermission != null)
                {
                    // Update existing permission
                    existingPermission.IsActive = true;
                    existingPermission.ExpiryDate = expiryDate;
                    existingPermission.GrantedById = grantedById;
                    existingPermission.GrantedAt = DateTime.UtcNow;
                    existingPermission.Notes = notes;
                }
                else
                {
                    // Create new permission
                    var newPermission = new DelegatedPermission
                    {
                        UserId = userId,
                        AccessRight = accessRight,
                        GrantedById = grantedById,
                        ExpiryDate = expiryDate,
                        Notes = notes,
                        IsActive = true,
                        GrantedAt = DateTime.UtcNow
                    };

                    _context.DelegatedPermissions.Add(newPermission);
                }

                await _context.SaveChangesAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> RevokePermissionAsync(string userId, string accessRight)
        {
            try
            {
                var permission = await _context.DelegatedPermissions
                    .FirstOrDefaultAsync(dp => dp.UserId == userId && dp.AccessRight == accessRight);

                if (permission != null)
                {
                    permission.IsActive = false;
                    await _context.SaveChangesAsync();
                    return true;
                }

                return false;
            }
            catch
            {
                return false;
            }
        }

        public async Task<List<DelegatedPermission>> GetAllPermissionsAsync()
        {
            return await _context.DelegatedPermissions
                .Include(dp => dp.User)
                .Include(dp => dp.GrantedBy)
                .OrderByDescending(dp => dp.GrantedAt)
                .ToListAsync();
        }

        public async Task<bool> UpdatePermissionAsync(int permissionId, DateTime? newExpiryDate, string? notes = null)
        {
            try
            {
                var permission = await _context.DelegatedPermissions.FindAsync(permissionId);
                if (permission != null)
                {
                    permission.ExpiryDate = newExpiryDate;
                    if (notes != null)
                        permission.Notes = notes;

                    await _context.SaveChangesAsync();
                    return true;
                }

                return false;
            }
            catch
            {
                return false;
            }
        }
    }
}
