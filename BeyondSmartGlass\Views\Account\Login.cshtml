@model BeyondSmartGlass.Controllers.LoginViewModel
@using Microsoft.AspNetCore.Mvc.Localization
@inject IViewLocalizer Localizer

@{
    ViewData["Title"] = Localizer["Login"];
}

<div class="row justify-content-center">
    <div class="col-md-6 col-lg-4">
        <div class="card shadow">
            <div class="card-header bg-primary text-white text-center">
                <h4>@Localizer["Login"]</h4>
            </div>
            <div class="card-body">
                <form asp-action="Login" asp-route-returnurl="@ViewData["ReturnUrl"]" method="post">
                    <div asp-validation-summary="All" class="text-danger"></div>
                    
                    <div class="form-group mb-3">
                        <label asp-for="Email" class="form-label">@Localizer["Email"]</label>
                        <input asp-for="Email" class="form-control" />
                        <span asp-validation-for="Email" class="text-danger"></span>
                    </div>
                    
                    <div class="form-group mb-3">
                        <label asp-for="Password" class="form-label">@Localizer["Password"]</label>
                        <input asp-for="Password" class="form-control" />
                        <span asp-validation-for="Password" class="text-danger"></span>
                    </div>
                    
                    <div class="form-check mb-3">
                        <input asp-for="RememberMe" class="form-check-input" />
                        <label asp-for="RememberMe" class="form-check-label">@Localizer["RememberMe"]</label>
                    </div>
                    
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">@Localizer["Login"]</button>
                    </div>
                </form>
                
                <hr />
                
                <div class="text-center">
                    <a asp-action="Register" class="btn btn-link">@Localizer["CreateNewAccount"]</a>
                </div>
            </div>
        </div>
        
        <!-- Language Toggle -->
        <div class="text-center mt-3">
            <form asp-controller="Account" asp-action="SetLanguage" asp-route-returnUrl="@Context.Request.Path">
                <div class="btn-group" role="group">
                    <input type="hidden" name="returnUrl" value="@Context.Request.Path" />
                    <button type="submit" name="culture" value="en-US" class="btn btn-outline-secondary btn-sm">English</button>
                    <button type="submit" name="culture" value="ar-SA" class="btn btn-outline-secondary btn-sm">العربية</button>
                </div>
            </form>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
