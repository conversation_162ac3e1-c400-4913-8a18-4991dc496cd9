@model BeyondSmartGlass.Controllers.RegisterViewModel
@using Microsoft.AspNetCore.Mvc.Localization
@inject IViewLocalizer Localizer

@{
    ViewData["Title"] = Localizer["Register"];
}

<div class="row justify-content-center">
    <div class="col-md-8 col-lg-6">
        <div class="card shadow">
            <div class="card-header bg-primary text-white text-center">
                <h4>@Localizer["Register"]</h4>
            </div>
            <div class="card-body">
                <form asp-action="Register" method="post">
                    <div asp-validation-summary="All" class="text-danger"></div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label asp-for="FirstName" class="form-label">@Localizer["FirstName"]</label>
                                <input asp-for="FirstName" class="form-control" />
                                <span asp-validation-for="FirstName" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label asp-for="LastName" class="form-label">@Localizer["LastName"]</label>
                                <input asp-for="LastName" class="form-control" />
                                <span asp-validation-for="LastName" class="text-danger"></span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group mb-3">
                        <label asp-for="Email" class="form-label">@Localizer["Email"]</label>
                        <input asp-for="Email" class="form-control" />
                        <span asp-validation-for="Email" class="text-danger"></span>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label asp-for="Phone" class="form-label">@Localizer["Phone"]</label>
                                <input asp-for="Phone" class="form-control" />
                                <span asp-validation-for="Phone" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label asp-for="City" class="form-label">@Localizer["City"]</label>
                                <input asp-for="City" class="form-control" />
                                <span asp-validation-for="City" class="text-danger"></span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label asp-for="Password" class="form-label">@Localizer["Password"]</label>
                                <input asp-for="Password" class="form-control" />
                                <span asp-validation-for="Password" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label asp-for="ConfirmPassword" class="form-label">@Localizer["ConfirmPassword"]</label>
                                <input asp-for="ConfirmPassword" class="form-control" />
                                <span asp-validation-for="ConfirmPassword" class="text-danger"></span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">@Localizer["Register"]</button>
                    </div>
                </form>
                
                <hr />
                
                <div class="text-center">
                    <a asp-action="Login" class="btn btn-link">@Localizer["AlreadyHaveAccount"]</a>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
