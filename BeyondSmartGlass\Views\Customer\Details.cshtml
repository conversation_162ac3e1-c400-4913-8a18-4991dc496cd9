@model BeyondSmartGlass.Models.Customer
@using Microsoft.AspNetCore.Mvc.Localization
@inject IViewLocalizer Localizer

@{
    ViewData["Title"] = Localizer["CustomerDetails"];
}

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header bg-info text-white">
                <h4 class="mb-0">
                    <i class="fas fa-user me-2"></i>@Localizer["CustomerDetails"]
                </h4>
            </div>
            <div class="card-body">
                <dl class="row">
                    <dt class="col-sm-3">@Localizer["Name"]:</dt>
                    <dd class="col-sm-9">@Model.Name</dd>
                    
                    <dt class="col-sm-3">@Localizer["City"]:</dt>
                    <dd class="col-sm-9">@Model.City</dd>
                    
                    <dt class="col-sm-3">@Localizer["Phone"]:</dt>
                    <dd class="col-sm-9">@(Model.Phone ?? "-")</dd>
                    
                    <dt class="col-sm-3">@Localizer["Email"]:</dt>
                    <dd class="col-sm-9">@(Model.Email ?? "-")</dd>
                    
                    <dt class="col-sm-3">@Localizer["Address"]:</dt>
                    <dd class="col-sm-9">@(Model.Address ?? "-")</dd>
                    
                    <dt class="col-sm-3">@Localizer["Status"]:</dt>
                    <dd class="col-sm-9">
                        <span class="badge @(Model.IsActive ? "bg-success" : "bg-danger")">
                            @(Model.IsActive ? Localizer["Active"] : Localizer["Inactive"])
                        </span>
                    </dd>
                    
                    <dt class="col-sm-3">@Localizer["CreatedAt"]:</dt>
                    <dd class="col-sm-9">@Model.CreatedAt.ToString("dd/MM/yyyy HH:mm")</dd>
                    
                    <dt class="col-sm-3">@Localizer["UpdatedAt"]:</dt>
                    <dd class="col-sm-9">@Model.UpdatedAt.ToString("dd/MM/yyyy HH:mm")</dd>
                </dl>
                
                <div class="d-flex justify-content-between">
                    <a asp-action="Index" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-1"></i>@Localizer["Back"]
                    </a>
                    <div>
                        <a asp-action="Edit" asp-route-id="@Model.Id" class="btn btn-warning me-2">
                            <i class="fas fa-edit me-1"></i>@Localizer["Edit"]
                        </a>
                        <a asp-controller="Quotation" asp-action="Create" asp-route-customerId="@Model.Id" class="btn btn-primary">
                            <i class="fas fa-file-invoice me-1"></i>@Localizer["CreateQuotation"]
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <!-- Summary Cards -->
        <div class="card mb-3">
            <div class="card-header">
                <h6 class="mb-0">@Localizer["Summary"]</h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-4">
                        <div class="border-end">
                            <h5 class="text-primary mb-0">@Model.Quotations.Count</h5>
                            <small class="text-muted">@Localizer["Quotations"]</small>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="border-end">
                            <h5 class="text-success mb-0">@Model.Contracts.Count</h5>
                            <small class="text-muted">@Localizer["Contracts"]</small>
                        </div>
                    </div>
                    <div class="col-4">
                        <h5 class="text-info mb-0">@Model.Projects.Count</h5>
                        <small class="text-muted">@Localizer["Projects"]</small>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Recent Activity -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">@Localizer["RecentActivity"]</h6>
            </div>
            <div class="card-body">
                @if (Model.Quotations.Any() || Model.Contracts.Any() || Model.Projects.Any())
                {
                    <div class="timeline">
                        @foreach (var quotation in Model.Quotations.OrderByDescending(q => q.CreatedAt).Take(3))
                        {
                            <div class="timeline-item">
                                <small class="text-muted">@quotation.CreatedAt.ToString("dd/MM/yyyy")</small>
                                <p class="mb-1">@Localizer["QuotationCreated"]: @quotation.QuotationNumber</p>
                            </div>
                        }
                        @foreach (var contract in Model.Contracts.OrderByDescending(c => c.CreatedAt).Take(2))
                        {
                            <div class="timeline-item">
                                <small class="text-muted">@contract.CreatedAt.ToString("dd/MM/yyyy")</small>
                                <p class="mb-1">@Localizer["ContractSigned"]: @contract.ContractNumber</p>
                            </div>
                        }
                    </div>
                }
                else
                {
                    <p class="text-muted text-center">@Localizer["NoRecentActivity"]</p>
                }
            </div>
        </div>
    </div>
</div>
