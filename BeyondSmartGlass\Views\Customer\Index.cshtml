@model IEnumerable<BeyondSmartGlass.Models.Customer>
@using Microsoft.AspNetCore.Mvc.Localization
@inject IViewLocalizer Localizer

@{
    ViewData["Title"] = Localizer["Customers"];
}

<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-users me-2"></i>@Localizer["Customers"]</h2>
    <a asp-action="Create" class="btn btn-primary">
        <i class="fas fa-plus me-1"></i>@Localizer["Add"] @Localizer["Customer"]
    </a>
</div>

@if (TempData["SuccessMessage"] != null)
{
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        @TempData["SuccessMessage"]
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
}

<!-- Search and Filter -->
<div class="card mb-4">
    <div class="card-body">
        <form method="get" class="row g-3">
            <div class="col-md-4">
                <label for="searchString" class="form-label">@Localizer["Search"]</label>
                <input type="text" class="form-control" id="searchString" name="searchString" 
                       value="@ViewBag.SearchString" placeholder="@Localizer["SearchCustomers"]">
            </div>
            <div class="col-md-3">
                <label for="cityFilter" class="form-label">@Localizer["City"]</label>
                <select class="form-select" id="cityFilter" name="cityFilter">
                    <option value="">@Localizer["AllCities"]</option>
                    @foreach (var city in ViewBag.Cities as List<string>)
                    {
                        <option value="@city" selected="@(city == ViewBag.CityFilter)">@city</option>
                    }
                </select>
            </div>
            <div class="col-md-2 d-flex align-items-end">
                <button type="submit" class="btn btn-outline-primary me-2">
                    <i class="fas fa-search me-1"></i>@Localizer["Search"]
                </button>
                <a href="@Url.Action("Index")" class="btn btn-outline-secondary">
                    <i class="fas fa-times me-1"></i>@Localizer["Clear"]
                </a>
            </div>
        </form>
    </div>
</div>

<!-- Customers Table -->
<div class="card">
    <div class="card-body">
        @if (Model.Any())
        {
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead class="table-light">
                        <tr>
                            <th>@Localizer["Name"]</th>
                            <th>@Localizer["City"]</th>
                            <th>@Localizer["Phone"]</th>
                            <th>@Localizer["Email"]</th>
                            <th>@Localizer["CreatedAt"]</th>
                            <th>@Localizer["Actions"]</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var customer in Model)
                        {
                            <tr>
                                <td>
                                    <strong>@customer.Name</strong>
                                </td>
                                <td>@customer.City</td>
                                <td>@customer.Phone</td>
                                <td>@customer.Email</td>
                                <td>@customer.CreatedAt.ToString("dd/MM/yyyy")</td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a asp-action="Details" asp-route-id="@customer.Id" 
                                           class="btn btn-sm btn-outline-info" title="@Localizer["Details"]">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a asp-action="Edit" asp-route-id="@customer.Id" 
                                           class="btn btn-sm btn-outline-warning" title="@Localizer["Edit"]">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a asp-action="Delete" asp-route-id="@customer.Id" 
                                           class="btn btn-sm btn-outline-danger" title="@Localizer["Delete"]">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        }
        else
        {
            <div class="text-center py-5">
                <i class="fas fa-users fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">@Localizer["NoCustomersFound"]</h5>
                <p class="text-muted">@Localizer["NoCustomersMessage"]</p>
                <a asp-action="Create" class="btn btn-primary">
                    <i class="fas fa-plus me-1"></i>@Localizer["AddFirstCustomer"]
                </a>
            </div>
        }
    </div>
</div>
