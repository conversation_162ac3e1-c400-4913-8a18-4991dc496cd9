@model DashboardViewModel
@using Microsoft.AspNetCore.Mvc.Localization
@inject IViewLocalizer Localizer
@{
    ViewData["Title"] = "Dashboard";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<div class="container-fluid dashboard-container">
    <!-- Header with Filters -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h2 class="dashboard-title">@Localizer["Dashboard"]</h2>
                <div class="filter-controls">
                    <div class="btn-group me-3" role="group">
                        <button type="button" class="btn btn-outline-primary filter-btn" data-period="weekly">@Localizer["Weekly"]</button>
                        <button type="button" class="btn btn-primary filter-btn" data-period="monthly">@Localizer["Monthly"]</button>
                        <button type="button" class="btn btn-outline-primary filter-btn" data-period="yearly">@Localizer["Yearly"]</button>
                        <button type="button" class="btn btn-outline-primary filter-btn" data-period="custom">@Localizer["Custom"]</button>
                    </div>
                    <select class="form-select year-selector" style="width: 120px; display: inline-block;">
                        @for (int year = DateTime.Now.Year; year >= DateTime.Now.Year - 5; year--)
                        {
                            if (year == Model.SelectedYear)
                            {
                                <option value="@year" selected>@year</option>
                            }
                            else
                            {
                                <option value="@year">@year</option>
                            }
                        }
                    </select>
                </div>
            </div>
        </div>
    </div>

    <!-- Custom Date Range (Hidden by default) -->
    <div class="row mb-3 custom-date-range" style="display: none;">
        <div class="col-md-6">
            <div class="row">
                <div class="col-6">
                    <label class="form-label">@Localizer["Start Date"]</label>
                    <input type="date" class="form-control" id="startDate" value="@Model.StartDate.ToString("yyyy-MM-dd")">
                </div>
                <div class="col-6">
                    <label class="form-label">@Localizer["End Date"]</label>
                    <input type="date" class="form-control" id="endDate" value="@Model.EndDate.ToString("yyyy-MM-dd")">
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <label class="form-label">&nbsp;</label>
            <div>
                <button type="button" class="btn btn-primary" id="applyCustomFilter">@Localizer["Apply Filter"]</button>
            </div>
        </div>
    </div>

    <!-- KPI Cards Row -->
    <div class="row mb-4">
        <!-- Total Contracts -->
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="kpi-card card h-100">
                <div class="card-body text-center">
                    <div class="kpi-icon contracts-icon">
                        <i class="fas fa-file-contract"></i>
                    </div>
                    <h3 class="kpi-value" id="totalContracts">@Model.DashboardData.TotalContracts</h3>
                    <p class="kpi-label">@Localizer["Total Contracts"]</p>
                </div>
            </div>
        </div>

        <!-- Total Quotations -->
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="kpi-card card h-100">
                <div class="card-body text-center">
                    <div class="kpi-icon quotations-icon">
                        <i class="fas fa-quote-left"></i>
                    </div>
                    <h3 class="kpi-value" id="totalQuotations">@Model.DashboardData.TotalQuotations</h3>
                    <p class="kpi-label">@Localizer["Total Quotations"]</p>
                </div>
            </div>
        </div>

        <!-- Open Maintenance -->
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="kpi-card card h-100">
                <div class="card-body text-center">
                    <div class="kpi-icon maintenance-icon">
                        <i class="fas fa-tools"></i>
                    </div>
                    <h3 class="kpi-value" id="openMaintenance">@Model.DashboardData.OpenMaintenanceRequests</h3>
                    <p class="kpi-label">@Localizer["Open Maintenance"]</p>
                </div>
            </div>
        </div>

        <!-- Total Sales -->
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="kpi-card card h-100">
                <div class="card-body text-center">
                    <div class="kpi-icon sales-icon">
                        <i class="fas fa-dollar-sign"></i>
                    </div>
                    <h3 class="kpi-value" id="totalSales">@Model.DashboardData.TotalSalesAmount.ToString("C")</h3>
                    <p class="kpi-label">@Localizer["Total Sales"]</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row -->
    <div class="row mb-4">
        <!-- Contract Status Chart -->
        <div class="col-lg-4 col-md-6 mb-3">
            <div class="card chart-card h-100">
                <div class="card-header">
                    <h5 class="card-title">@Localizer["Contract Status"]</h5>
                </div>
                <div class="card-body">
                    <canvas id="contractStatusChart" width="400" height="300"></canvas>
                </div>
            </div>
        </div>

        <!-- Project Status Chart -->
        <div class="col-lg-4 col-md-6 mb-3">
            <div class="card chart-card h-100">
                <div class="card-header">
                    <h5 class="card-title">@Localizer["Project Status"]</h5>
                </div>
                <div class="card-body">
                    <canvas id="projectStatusChart" width="400" height="300"></canvas>
                </div>
            </div>
        </div>

        <!-- Maintenance Status Chart -->
        <div class="col-lg-4 col-md-6 mb-3">
            <div class="card chart-card h-100">
                <div class="card-header">
                    <h5 class="card-title">@Localizer["Maintenance Status"]</h5>
                </div>
                <div class="card-body">
                    <canvas id="maintenanceStatusChart" width="400" height="300"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Monthly Trend Chart -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card chart-card">
                <div class="card-header">
                    <h5 class="card-title">@Localizer["Monthly Trends"]</h5>
                </div>
                <div class="card-body">
                    <canvas id="monthlyTrendChart" width="400" height="200"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Additional KPIs Row -->
    <div class="row">
        <!-- Active Projects -->
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="kpi-card card h-100">
                <div class="card-body text-center">
                    <div class="kpi-icon projects-icon">
                        <i class="fas fa-project-diagram"></i>
                    </div>
                    <h3 class="kpi-value" id="activeProjects">@Model.DashboardData.ActiveProjects</h3>
                    <p class="kpi-label">@Localizer["Active Projects"]</p>
                </div>
            </div>
        </div>

        <!-- Closed Projects -->
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="kpi-card card h-100">
                <div class="card-body text-center">
                    <div class="kpi-icon completed-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <h3 class="kpi-value" id="closedProjects">@Model.DashboardData.ClosedProjects</h3>
                    <p class="kpi-label">@Localizer["Closed Projects"]</p>
                </div>
            </div>
        </div>

        <!-- Closed Maintenance -->
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="kpi-card card h-100">
                <div class="card-body text-center">
                    <div class="kpi-icon maintenance-closed-icon">
                        <i class="fas fa-wrench"></i>
                    </div>
                    <h3 class="kpi-value" id="closedMaintenance">@Model.DashboardData.ClosedMaintenanceRequests</h3>
                    <p class="kpi-label">@Localizer["Closed Maintenance"]</p>
                </div>
            </div>
        </div>

        <!-- Export Options -->
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card h-100">
                <div class="card-body text-center">
                    <h5 class="card-title">@Localizer["Export Data"]</h5>
                    <div class="d-grid gap-2">
                        <button type="button" class="btn btn-outline-primary btn-sm" id="exportCsv">
                            <i class="fas fa-file-csv"></i> @Localizer["Export CSV"]
                        </button>
                        <button type="button" class="btn btn-outline-success btn-sm" id="exportJson">
                            <i class="fas fa-file-code"></i> @Localizer["Export JSON"]
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Loading Overlay -->
<div class="loading-overlay" id="loadingOverlay" style="display: none;">
    <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">@Localizer["Loading..."]</span>
    </div>
</div>

@section Styles {
    <style>
        .dashboard-container {
            background-color: #f8f9fa;
            min-height: 100vh;
            padding: 20px;
        }

        .dashboard-title {
            color: #2c3e50;
            font-weight: 600;
            margin-bottom: 0;
        }

        .filter-controls {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .kpi-card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .kpi-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 15px rgba(0, 0, 0, 0.2);
        }

        .kpi-icon {
            font-size: 2.5rem;
            margin-bottom: 15px;
            opacity: 0.8;
        }

        .contracts-icon { color: #3498db; }
        .quotations-icon { color: #e74c3c; }
        .maintenance-icon { color: #f39c12; }
        .sales-icon { color: #27ae60; }
        .projects-icon { color: #9b59b6; }
        .completed-icon { color: #2ecc71; }
        .maintenance-closed-icon { color: #34495e; }

        .kpi-value {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .kpi-label {
            font-size: 0.9rem;
            margin-bottom: 0;
            opacity: 0.9;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .chart-card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .chart-card .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0;
            border: none;
        }

        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.8);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
        }

        .filter-btn.active {
            background-color: #667eea;
            border-color: #667eea;
        }

        .year-selector {
            border-radius: 8px;
            border: 2px solid #667eea;
        }

        .custom-date-range {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        /* RTL Support */
        [dir="rtl"] .dashboard-container {
            text-align: right;
        }

        [dir="rtl"] .filter-controls {
            flex-direction: row-reverse;
        }

        /* Responsive Design */
        @@media (max-width: 768px) {
            .filter-controls {
                flex-direction: column;
                gap: 10px;
            }

            .kpi-value {
                font-size: 2rem;
            }

            .kpi-icon {
                font-size: 2rem;
            }
        }
    </style>
}

@section Scripts {
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        let contractChart, projectChart, maintenanceChart, monthlyChart;
        let currentPeriod = 'monthly';
        let currentYear = @Model.SelectedYear;

        $(document).ready(function() {
            initializeCharts();
            bindEvents();
        });

        function bindEvents() {
            // Filter button events
            $('.filter-btn').click(function() {
                $('.filter-btn').removeClass('btn-primary').addClass('btn-outline-primary');
                $(this).removeClass('btn-outline-primary').addClass('btn-primary');

                currentPeriod = $(this).data('period');

                if (currentPeriod === 'custom') {
                    $('.custom-date-range').show();
                } else {
                    $('.custom-date-range').hide();
                    loadDashboardData();
                }
            });

            // Year selector change
            $('.year-selector').change(function() {
                currentYear = $(this).val();
                loadDashboardData();
            });

            // Custom date filter
            $('#applyCustomFilter').click(function() {
                loadDashboardData();
            });

            // Export buttons
            $('#exportCsv').click(function() {
                exportData('csv');
            });

            $('#exportJson').click(function() {
                exportData('json');
            });
        }

        function loadDashboardData() {
            showLoading();

            let startDate = null;
            let endDate = null;

            if (currentPeriod === 'custom') {
                startDate = $('#startDate').val();
                endDate = $('#endDate').val();
            }

            $.ajax({
                url: '@Url.Action("FilterData", "Dashboard")',
                type: 'POST',
                data: {
                    period: currentPeriod,
                    startDate: startDate,
                    endDate: endDate,
                    year: currentYear
                },
                success: function(response) {
                    if (response.success) {
                        updateKPIs(response.data.dashboardData);
                        updateCharts(response.data);
                    } else {
                        showError(response.message);
                    }
                },
                error: function() {
                    showError('@Localizer["Error loading dashboard data"]');
                },
                complete: function() {
                    hideLoading();
                }
            });
        }

        function updateKPIs(data) {
            $('#totalContracts').text(data.totalContracts);
            $('#totalQuotations').text(data.totalQuotations);
            $('#openMaintenance').text(data.openMaintenanceRequests);
            $('#totalSales').text(formatCurrency(data.totalSalesAmount));
            $('#activeProjects').text(data.activeProjects);
            $('#closedProjects').text(data.closedProjects);
            $('#closedMaintenance').text(data.closedMaintenanceRequests);
        }

        function initializeCharts() {
            // Contract Status Chart
            const contractCtx = document.getElementById('contractStatusChart').getContext('2d');
            contractChart = new Chart(contractCtx, {
                type: 'doughnut',
                data: {
                    labels: [],
                    datasets: [{
                        data: [],
                        backgroundColor: ['#3498db', '#e74c3c', '#f39c12', '#27ae60', '#9b59b6']
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });

            // Project Status Chart
            const projectCtx = document.getElementById('projectStatusChart').getContext('2d');
            projectChart = new Chart(projectCtx, {
                type: 'doughnut',
                data: {
                    labels: [],
                    datasets: [{
                        data: [],
                        backgroundColor: ['#3498db', '#e74c3c', '#f39c12', '#27ae60', '#9b59b6']
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });

            // Maintenance Status Chart
            const maintenanceCtx = document.getElementById('maintenanceStatusChart').getContext('2d');
            maintenanceChart = new Chart(maintenanceCtx, {
                type: 'doughnut',
                data: {
                    labels: [],
                    datasets: [{
                        data: [],
                        backgroundColor: ['#3498db', '#e74c3c', '#f39c12', '#27ae60']
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });

            // Monthly Trend Chart
            const monthlyCtx = document.getElementById('monthlyTrendChart').getContext('2d');
            monthlyChart = new Chart(monthlyCtx, {
                type: 'line',
                data: {
                    labels: @Html.Raw(Json.Serialize(Model.MonthlyData.Select(m => m.MonthName))),
                    datasets: [
                        {
                            label: '@Localizer["Contracts"]',
                            data: @Html.Raw(Json.Serialize(Model.MonthlyData.Select(m => m.Contracts))),
                            borderColor: '#3498db',
                            backgroundColor: 'rgba(52, 152, 219, 0.1)',
                            tension: 0.4
                        },
                        {
                            label: '@Localizer["Quotations"]',
                            data: @Html.Raw(Json.Serialize(Model.MonthlyData.Select(m => m.Quotations))),
                            borderColor: '#e74c3c',
                            backgroundColor: 'rgba(231, 76, 60, 0.1)',
                            tension: 0.4
                        },
                        {
                            label: '@Localizer["Completed Projects"]',
                            data: @Html.Raw(Json.Serialize(Model.MonthlyData.Select(m => m.CompletedProjects))),
                            borderColor: '#27ae60',
                            backgroundColor: 'rgba(39, 174, 96, 0.1)',
                            tension: 0.4
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    },
                    plugins: {
                        legend: {
                            position: 'top'
                        }
                    }
                }
            });
        }

        function updateCharts(data) {
            // Update contract status chart
            if (data.contractStatusData && data.contractStatusData.length > 0) {
                contractChart.data.labels = data.contractStatusData.map(item => item.label);
                contractChart.data.datasets[0].data = data.contractStatusData.map(item => item.value);
                contractChart.update();
            }

            // Update project status chart
            if (data.projectStatusData && data.projectStatusData.length > 0) {
                projectChart.data.labels = data.projectStatusData.map(item => item.label);
                projectChart.data.datasets[0].data = data.projectStatusData.map(item => item.value);
                projectChart.update();
            }

            // Update maintenance status chart
            if (data.maintenanceStatusData && data.maintenanceStatusData.length > 0) {
                maintenanceChart.data.labels = data.maintenanceStatusData.map(item => item.label);
                maintenanceChart.data.datasets[0].data = data.maintenanceStatusData.map(item => item.value);
                maintenanceChart.update();
            }

            // Update monthly trend chart
            if (data.monthlyData && data.monthlyData.length > 0) {
                monthlyChart.data.labels = data.monthlyData.map(item => item.monthName);
                monthlyChart.data.datasets[0].data = data.monthlyData.map(item => item.contracts);
                monthlyChart.data.datasets[1].data = data.monthlyData.map(item => item.quotations);
                monthlyChart.data.datasets[2].data = data.monthlyData.map(item => item.completedProjects);
                monthlyChart.update();
            }
        }

        function exportData(format) {
            let startDate = null;
            let endDate = null;

            if (currentPeriod === 'custom') {
                startDate = $('#startDate').val();
                endDate = $('#endDate').val();
            } else {
                // Calculate dates based on current period
                const now = new Date();
                switch (currentPeriod) {
                    case 'weekly':
                        const startOfWeek = new Date(now.setDate(now.getDate() - now.getDay()));
                        startDate = startOfWeek.toISOString().split('T')[0];
                        endDate = new Date(startOfWeek.getTime() + 6 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];
                        break;
                    case 'monthly':
                        startDate = new Date(now.getFullYear(), now.getMonth(), 1).toISOString().split('T')[0];
                        endDate = new Date(now.getFullYear(), now.getMonth() + 1, 0).toISOString().split('T')[0];
                        break;
                    case 'yearly':
                        startDate = new Date(currentYear, 0, 1).toISOString().split('T')[0];
                        endDate = new Date(currentYear, 11, 31).toISOString().split('T')[0];
                        break;
                }
            }

            const url = '@Url.Action("ExportData", "Dashboard")' +
                       `?format=${format}&startDate=${startDate}&endDate=${endDate}`;

            if (format === 'csv') {
                window.open(url, '_blank');
            } else {
                fetch(url)
                    .then(response => response.json())
                    .then(data => {
                        const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
                        const url = window.URL.createObjectURL(blob);
                        const a = document.createElement('a');
                        a.href = url;
                        a.download = `dashboard-data-${startDate}-to-${endDate}.json`;
                        a.click();
                        window.URL.revokeObjectURL(url);
                    });
            }
        }

        function formatCurrency(amount) {
            return new Intl.NumberFormat('@System.Globalization.CultureInfo.CurrentCulture.Name', {
                style: 'currency',
                currency: 'SAR'
            }).format(amount);
        }

        function showLoading() {
            $('#loadingOverlay').show();
        }

        function hideLoading() {
            $('#loadingOverlay').hide();
        }

        function showError(message) {
            // You can implement a toast notification or alert here
            alert(message);
        }
    </script>
}
