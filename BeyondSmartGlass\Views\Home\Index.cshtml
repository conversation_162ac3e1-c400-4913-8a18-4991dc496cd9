﻿@model BeyondSmartGlass.Models.ViewModels.DashboardViewModel
@using Microsoft.AspNetCore.Mvc.Localization
@inject IViewLocalizer Localizer

@{
    ViewData["Title"] = Localizer["Dashboard"];
}

<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-tachometer-alt me-2"></i>@Localizer["Dashboard"]</h2>
    <div class="text-muted">
        <i class="fas fa-clock me-1"></i>@DateTime.Now.ToString("dd/MM/yyyy HH:mm")
    </div>
</div>

<!-- KPI Cards -->
<div class="row g-3 mb-4">
    <div class="col-xl-3 col-md-6">
        <div class="card dashboard-card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0">@Model.KPIs.TotalProjects</h4>
                        <p class="mb-0">@Localizer["TotalProjects"]</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-project-diagram fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6">
        <div class="card dashboard-card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0">@Model.KPIs.ActiveProjects</h4>
                        <p class="mb-0">@Localizer["ActiveProjects"]</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-play-circle fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6">
        <div class="card dashboard-card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0">@Model.KPIs.PendingQuotations</h4>
                        <p class="mb-0">@Localizer["PendingQuotations"]</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-file-invoice fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6">
        <div class="card dashboard-card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0">@Model.KPIs.PendingInstallations</h4>
                        <p class="mb-0">@Localizer["PendingInstallations"]</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-tools fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Revenue Cards -->
<div class="row g-3 mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-body">
                <h6 class="card-title text-success">@Localizer["TotalRevenue"]</h6>
                <h3 class="text-success mb-0">@Model.KPIs.TotalRevenue.ToString("C")</h3>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card">
            <div class="card-body">
                <h6 class="card-title text-warning">@Localizer["PendingRevenue"]</h6>
                <h3 class="text-warning mb-0">@Model.KPIs.PendingRevenue.ToString("C")</h3>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Project Status Chart -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">@Localizer["ProjectsByStatus"]</h6>
            </div>
            <div class="card-body">
                @if (Model.ProjectsByStatus.Any())
                {
                    <canvas id="projectStatusChart" width="400" height="200"></canvas>
                }
                else
                {
                    <div class="text-center py-4">
                        <i class="fas fa-chart-pie fa-3x text-muted mb-3"></i>
                        <p class="text-muted">@Localizer["NoProjectData"]</p>
                    </div>
                }
            </div>
        </div>
    </div>

    <!-- Material Shortages -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">@Localizer["MaterialShortages"]</h6>
            </div>
            <div class="card-body">
                @if (Model.MaterialShortages.Any())
                {
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>@Localizer["Material"]</th>
                                    <th>@Localizer["Current"]</th>
                                    <th>@Localizer["Minimum"]</th>
                                    <th>@Localizer["Shortage"]</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var shortage in Model.MaterialShortages)
                                {
                                    <tr>
                                        <td>
                                            <small class="fw-bold">@shortage.MaterialName</small><br>
                                            <small class="text-muted">@shortage.ItemCode</small>
                                        </td>
                                        <td>@shortage.CurrentStock @shortage.Unit</td>
                                        <td>@shortage.MinimumStock @shortage.Unit</td>
                                        <td><span class="badge bg-danger">@shortage.ShortageQuantity @shortage.Unit</span></td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                }
                else
                {
                    <div class="text-center py-4">
                        <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                        <p class="text-muted">@Localizer["NoMaterialShortages"]</p>
                    </div>
                }
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        // Project Status Chart
        @if (Model.ProjectsByStatus.Any())
        {
            <text>
            const ctx = document.getElementById('projectStatusChart').getContext('2d');
            const projectStatusChart = new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: [@Html.Raw(string.Join(",", Model.ProjectsByStatus.Select(p => $"'{p.StatusName}'")))],
                    datasets: [{
                        data: [@string.Join(",", Model.ProjectsByStatus.Select(p => p.Count))],
                        backgroundColor: [
                            '#6c757d', // NotStarted - secondary
                            '#007bff', // InProgress - primary
                            '#ffc107', // WaitingMaterials - warning
                            '#17a2b8', // Installed - info
                            '#28a745', // Delivered - success
                            '#fd7e14', // OnHold - warning
                            '#dc3545'  // Cancelled - danger
                        ],
                        borderWidth: 2,
                        borderColor: '#fff'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
            </text>
        }
    </script>
}
