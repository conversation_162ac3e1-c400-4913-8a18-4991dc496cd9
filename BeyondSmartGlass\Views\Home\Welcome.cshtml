@using Microsoft.AspNetCore.Mvc.Localization
@inject IViewLocalizer Localizer

@{
    ViewData["Title"] = Localizer["Welcome"];
}

<div class="hero-section bg-primary text-white py-5 mb-5">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6">
                <h1 class="display-4 fw-bold mb-4">@Localizer["AppName"]</h1>
                <p class="lead mb-4">@Localizer["WelcomeMessage"]</p>
                <div class="d-flex gap-3">
                    <a asp-controller="Account" asp-action="Login" class="btn btn-light btn-lg">
                        <i class="fas fa-sign-in-alt me-2"></i>@Localizer["Login"]
                    </a>
                    <a asp-controller="Account" asp-action="Register" class="btn btn-outline-light btn-lg">
                        <i class="fas fa-user-plus me-2"></i>@Localizer["Register"]
                    </a>
                </div>
            </div>
            <div class="col-lg-6 text-center">
                <i class="fas fa-glass-whiskey fa-10x opacity-75"></i>
            </div>
        </div>
    </div>
</div>

<div class="container">
    <div class="row">
        <div class="col-lg-8 mx-auto text-center mb-5">
            <h2 class="mb-4">@Localizer["WhyChooseUs"]</h2>
            <p class="lead text-muted">@Localizer["WhyChooseUsDescription"]</p>
        </div>
    </div>
    
    <div class="row g-4 mb-5">
        <div class="col-md-4">
            <div class="card h-100 border-0 shadow-sm">
                <div class="card-body text-center p-4">
                    <div class="bg-primary bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 80px; height: 80px;">
                        <i class="fas fa-users fa-2x text-primary"></i>
                    </div>
                    <h5 class="card-title">@Localizer["CustomerManagement"]</h5>
                    <p class="card-text text-muted">@Localizer["CustomerManagementDesc"]</p>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card h-100 border-0 shadow-sm">
                <div class="card-body text-center p-4">
                    <div class="bg-success bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 80px; height: 80px;">
                        <i class="fas fa-project-diagram fa-2x text-success"></i>
                    </div>
                    <h5 class="card-title">@Localizer["ProjectTracking"]</h5>
                    <p class="card-text text-muted">@Localizer["ProjectTrackingDesc"]</p>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card h-100 border-0 shadow-sm">
                <div class="card-body text-center p-4">
                    <div class="bg-info bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 80px; height: 80px;">
                        <i class="fas fa-warehouse fa-2x text-info"></i>
                    </div>
                    <h5 class="card-title">@Localizer["InventoryManagement"]</h5>
                    <p class="card-text text-muted">@Localizer["InventoryManagementDesc"]</p>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row">
        <div class="col-lg-6">
            <div class="card border-0 shadow-sm">
                <div class="card-body p-4">
                    <h5 class="card-title">@Localizer["Features"]</h5>
                    <ul class="list-unstyled">
                        <li class="mb-2"><i class="fas fa-check text-success me-2"></i>@Localizer["BilingualSupport"]</li>
                        <li class="mb-2"><i class="fas fa-check text-success me-2"></i>@Localizer["RoleBasedAccess"]</li>
                        <li class="mb-2"><i class="fas fa-check text-success me-2"></i>@Localizer["RealTimeTracking"]</li>
                        <li class="mb-2"><i class="fas fa-check text-success me-2"></i>@Localizer["ComprehensiveReporting"]</li>
                        <li class="mb-2"><i class="fas fa-check text-success me-2"></i>@Localizer["WarrantyManagement"]</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="col-lg-6">
            <div class="card border-0 shadow-sm">
                <div class="card-body p-4">
                    <h5 class="card-title">@Localizer["GetStarted"]</h5>
                    <p class="text-muted">@Localizer["GetStartedDesc"]</p>
                    <a asp-controller="Account" asp-action="Register" class="btn btn-primary">
                        <i class="fas fa-rocket me-2"></i>@Localizer["StartNow"]
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.hero-section {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
}

.fa-10x {
    font-size: 10em;
}
</style>
