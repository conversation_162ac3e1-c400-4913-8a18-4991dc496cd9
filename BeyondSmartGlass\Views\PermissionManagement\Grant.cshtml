@model GrantPermissionViewModel
@using Microsoft.AspNetCore.Mvc.Localization
@inject IViewLocalizer Localizer
@{
    ViewData["Title"] = "Grant Permission";
}

<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-shield-alt me-2"></i>@Localizer["Grant Permission"]
                    </h5>
                </div>
                <div class="card-body">
                    <form asp-action="Grant" method="post">
                        <div asp-validation-summary="ModelOnly" class="alert alert-danger" role="alert"></div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label asp-for="UserId" class="form-label">@Localizer["User"] <span class="text-danger">*</span></label>
                                <select asp-for="UserId" class="form-select" asp-items="ViewBag.Users">
                                    <option value="">@Localizer["Select a user..."]</option>
                                </select>
                                <span asp-validation-for="UserId" class="text-danger"></span>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label asp-for="AccessRight" class="form-label">@Localizer["Access Right"] <span class="text-danger">*</span></label>
                                <select asp-for="AccessRight" class="form-select" asp-items="ViewBag.AccessRights">
                                    <option value="">@Localizer["Select an access right..."]</option>
                                </select>
                                <span asp-validation-for="AccessRight" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label asp-for="ExpiryDate" class="form-label">@Localizer["Expiry Date"]</label>
                                <input asp-for="ExpiryDate" type="date" class="form-control" />
                                <div class="form-text">@Localizer["Leave empty for permanent access"]</div>
                                <span asp-validation-for="ExpiryDate" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label asp-for="Notes" class="form-label">@Localizer["Notes"]</label>
                            <textarea asp-for="Notes" class="form-control" rows="3" 
                                      placeholder="@Localizer["Optional notes about this permission grant..."]"></textarea>
                            <span asp-validation-for="Notes" class="text-danger"></span>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a asp-action="Index" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-1"></i>@Localizer["Back to List"]
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-check me-1"></i>@Localizer["Grant Permission"]
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Access Rights Information -->
            <div class="card mt-4">
                <div class="card-header">
                    <h6 class="card-title mb-0">@Localizer["Available Access Rights"]</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <ul class="list-unstyled">
                                <li class="mb-2">
                                    <span class="badge bg-primary me-2">CanAccessDashboard</span>
                                    <small>@Localizer["Access to business dashboard and KPIs"]</small>
                                </li>
                                <li class="mb-2">
                                    <span class="badge bg-info me-2">CanManageUsers</span>
                                    <small>@Localizer["Manage user accounts and roles"]</small>
                                </li>
                                <li class="mb-2">
                                    <span class="badge bg-success me-2">CanViewReports</span>
                                    <small>@Localizer["Access to system reports"]</small>
                                </li>
                                <li class="mb-2">
                                    <span class="badge bg-warning me-2">CanManageCustomers</span>
                                    <small>@Localizer["Manage customer information"]</small>
                                </li>
                                <li class="mb-2">
                                    <span class="badge bg-danger me-2">CanManageProjects</span>
                                    <small>@Localizer["Manage projects and contracts"]</small>
                                </li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <ul class="list-unstyled">
                                <li class="mb-2">
                                    <span class="badge bg-secondary me-2">CanManageWarehouse</span>
                                    <small>@Localizer["Manage inventory and materials"]</small>
                                </li>
                                <li class="mb-2">
                                    <span class="badge bg-dark me-2">CanManageProduction</span>
                                    <small>@Localizer["Manage production orders"]</small>
                                </li>
                                <li class="mb-2">
                                    <span class="badge bg-light text-dark me-2">CanManageInstallation</span>
                                    <small>@Localizer["Manage installation schedules"]</small>
                                </li>
                                <li class="mb-2">
                                    <span class="badge bg-primary me-2">CanManageWarranty</span>
                                    <small>@Localizer["Manage warranties and maintenance"]</small>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
