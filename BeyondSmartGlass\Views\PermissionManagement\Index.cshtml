@model List<DelegatedPermission>
@using Microsoft.AspNetCore.Mvc.Localization
@inject IViewLocalizer Localizer
@{
    ViewData["Title"] = "Permission Management";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>@Localizer["Permission Management"]</h2>
                <a asp-action="Grant" class="btn btn-primary">
                    <i class="fas fa-plus"></i> @Localizer["Grant Permission"]
                </a>
            </div>

            @if (TempData["SuccessMessage"] != null)
            {
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    @TempData["SuccessMessage"]
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            }

            @if (TempData["ErrorMessage"] != null)
            {
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    @TempData["ErrorMessage"]
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            }

            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">@Localizer["Delegated Permissions"]</h5>
                </div>
                <div class="card-body">
                    @if (Model.Any())
                    {
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>@Localizer["User"]</th>
                                        <th>@Localizer["Access Right"]</th>
                                        <th>@Localizer["Granted At"]</th>
                                        <th>@Localizer["Granted By"]</th>
                                        <th>@Localizer["Expiry Date"]</th>
                                        <th>@Localizer["Status"]</th>
                                        <th>@Localizer["Actions"]</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var permission in Model)
                                    {
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="avatar-sm me-2">
                                                        <div class="avatar-title bg-primary rounded-circle">
                                                            @permission.User.FullName.Substring(0, 1).ToUpper()
                                                        </div>
                                                    </div>
                                                    <div>
                                                        <strong>@permission.User.FullName</strong>
                                                        <br>
                                                        <small class="text-muted">@permission.User.Email</small>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge bg-info">@permission.AccessRight</span>
                                            </td>
                                            <td>@permission.GrantedAt.ToString("yyyy-MM-dd HH:mm")</td>
                                            <td>
                                                @if (permission.GrantedBy != null)
                                                {
                                                    @permission.GrantedBy.FullName
                                                }
                                                else
                                                {
                                                    <span class="text-muted">@Localizer["System"]</span>
                                                }
                                            </td>
                                            <td>
                                                @if (permission.ExpiryDate.HasValue)
                                                {
                                                    @permission.ExpiryDate.Value.ToString("yyyy-MM-dd")
                                                }
                                                else
                                                {
                                                    <span class="text-muted">@Localizer["Never"]</span>
                                                }
                                            </td>
                                            <td>
                                                @if (permission.IsValid)
                                                {
                                                    <span class="badge bg-success">@Localizer["Active"]</span>
                                                }
                                                else
                                                {
                                                    <span class="badge bg-danger">@Localizer["Inactive"]</span>
                                                }
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a asp-action="UserPermissions" asp-route-userId="@permission.UserId" 
                                                       class="btn btn-sm btn-outline-info" title="@Localizer["View User Permissions"]">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    @if (permission.IsActive)
                                                    {
                                                        <form asp-action="Revoke" method="post" class="d-inline">
                                                            <input type="hidden" name="userId" value="@permission.UserId" />
                                                            <input type="hidden" name="accessRight" value="@permission.AccessRight" />
                                                            <button type="submit" class="btn btn-sm btn-outline-danger" 
                                                                    title="@Localizer["Revoke Permission"]"
                                                                    onclick="return confirm('@Localizer["Are you sure you want to revoke this permission?"]')">
                                                                <i class="fas fa-times"></i>
                                                            </button>
                                                        </form>
                                                    }
                                                </div>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    }
                    else
                    {
                        <div class="text-center py-5">
                            <i class="fas fa-shield-alt fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">@Localizer["No permissions have been delegated yet."]</h5>
                            <p class="text-muted">@Localizer["Click the 'Grant Permission' button to start delegating permissions to users."]</p>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .avatar-sm {
        width: 40px;
        height: 40px;
    }

    .avatar-title {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
        font-size: 14px;
    }
</style>
