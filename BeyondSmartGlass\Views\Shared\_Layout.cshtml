﻿@using Microsoft.AspNetCore.Mvc.Localization
@using Microsoft.AspNetCore.Identity
@using System.Globalization
@using BeyondSmartGlass.Models
@inject IViewLocalizer Localizer
@inject SignInManager<ApplicationUser> SignInManager
@inject UserManager<ApplicationUser> UserManager

@{
    var culture = CultureInfo.CurrentCulture;
    var isRtl = culture.TextInfo.IsRightToLeft;
}

<!DOCTYPE html>
<html lang="@culture.Name" dir="@(isRtl ? "rtl" : "ltr")">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - @Localizer["AppName"]</title>
    <link rel="stylesheet" href="~/lib/bootstrap/dist/css/bootstrap.min.css" />
    @if (isRtl)
    {
        <link rel="stylesheet" href="~/lib/bootstrap/dist/css/bootstrap.rtl.min.css" />
    }
    <link rel="stylesheet" href="~/css/site.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/BeyondSmartGlass.styles.css" asp-append-version="true" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" />
</head>
<body>
    <header>
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary shadow-sm">
            <div class="container-fluid">
                <a class="navbar-brand fw-bold" asp-controller="Home" asp-action="Index">
                    <i class="fas fa-glass-whiskey me-2"></i>@Localizer["AppName"]
                </a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="collapse navbar-collapse" id="navbarNav">
                    @if (SignInManager.IsSignedIn(User))
                    {
                        <ul class="navbar-nav me-auto">
                            <li class="nav-item">
                                <a class="nav-link" asp-controller="Dashboard" asp-action="Index">
                                    <i class="fas fa-tachometer-alt me-1"></i>@Localizer["Dashboard"]
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" asp-controller="Customer" asp-action="Index">
                                    <i class="fas fa-users me-1"></i>@Localizer["Customers"]
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" asp-controller="Quotation" asp-action="Index">
                                    <i class="fas fa-file-invoice me-1"></i>@Localizer["Quotations"]
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" asp-controller="Contract" asp-action="Index">
                                    <i class="fas fa-handshake me-1"></i>@Localizer["Contracts"]
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" asp-controller="Project" asp-action="Index">
                                    <i class="fas fa-project-diagram me-1"></i>@Localizer["Projects"]
                                </a>
                            </li>
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                    <i class="fas fa-cogs me-1"></i>@Localizer["Operations"]
                                </a>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" asp-controller="Warehouse" asp-action="Index">
                                        <i class="fas fa-warehouse me-1"></i>@Localizer["Warehouse"]
                                    </a></li>
                                    <li><a class="dropdown-item" asp-controller="Production" asp-action="Index">
                                        <i class="fas fa-industry me-1"></i>@Localizer["Production"]
                                    </a></li>
                                    <li><a class="dropdown-item" asp-controller="Installation" asp-action="Index">
                                        <i class="fas fa-tools me-1"></i>@Localizer["Installation"]
                                    </a></li>
                                    <li><a class="dropdown-item" asp-controller="Warranty" asp-action="Index">
                                        <i class="fas fa-shield-alt me-1"></i>@Localizer["Warranty"]
                                    </a></li>
                                </ul>
                            </li>
                        </ul>
                    }

                    <ul class="navbar-nav">
                        <!-- Language Toggle -->
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-globe me-1"></i>
                                @(culture.Name == "ar-SA" ? "العربية" : "English")
                            </a>
                            <ul class="dropdown-menu">
                                <li>
                                    <form asp-controller="Account" asp-action="SetLanguage" asp-route-returnUrl="@Context.Request.Path" method="post" class="d-inline">
                                        <button type="submit" name="culture" value="en-US" class="dropdown-item">
                                            <i class="fas fa-flag-usa me-1"></i>English
                                        </button>
                                    </form>
                                </li>
                                <li>
                                    <form asp-controller="Account" asp-action="SetLanguage" asp-route-returnUrl="@Context.Request.Path" method="post" class="d-inline">
                                        <button type="submit" name="culture" value="ar-SA" class="dropdown-item">
                                            <i class="fas fa-flag me-1"></i>العربية
                                        </button>
                                    </form>
                                </li>
                            </ul>
                        </li>

                        @if (SignInManager.IsSignedIn(User))
                        {
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                    <i class="fas fa-user me-1"></i>@User.Identity!.Name
                                </a>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="#"><i class="fas fa-user-edit me-1"></i>@Localizer["Profile"]</a></li>
                                    @if (User.IsInRole("Admin"))
                                    {
                                        <li><hr class="dropdown-divider"></li>
                                        <li><a class="dropdown-item" asp-controller="PermissionManagement" asp-action="Index">
                                            <i class="fas fa-shield-alt me-1"></i>@Localizer["Permission Management"]
                                        </a></li>
                                    }
                                    <li><hr class="dropdown-divider"></li>
                                    <li>
                                        <form asp-controller="Account" asp-action="Logout" method="post" class="d-inline">
                                            <button type="submit" class="dropdown-item">
                                                <i class="fas fa-sign-out-alt me-1"></i>@Localizer["Logout"]
                                            </button>
                                        </form>
                                    </li>
                                </ul>
                            </li>
                        }
                        else
                        {
                            <li class="nav-item">
                                <a class="nav-link" asp-controller="Account" asp-action="Login">
                                    <i class="fas fa-sign-in-alt me-1"></i>@Localizer["Login"]
                                </a>
                            </li>
                        }
                    </ul>
                </div>
            </div>
        </nav>
    </header>
    <div class="container">
        <main role="main" class="pb-3">
            @RenderBody()
        </main>
    </div>

    <footer class="bg-dark text-light py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5>@Localizer["AppName"]</h5>
                    <p class="mb-0">@Localizer["FooterDescription"]</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="mb-0">&copy; 2025 @Localizer["AppName"]. @Localizer["AllRightsReserved"]</p>
                </div>
            </div>
        </div>
    </footer>
    <script src="~/lib/jquery/dist/jquery.min.js"></script>
    <script src="~/lib/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
    <script src="~/js/site.js" asp-append-version="true"></script>
    @await RenderSectionAsync("Scripts", required: false)
</body>
</html>
