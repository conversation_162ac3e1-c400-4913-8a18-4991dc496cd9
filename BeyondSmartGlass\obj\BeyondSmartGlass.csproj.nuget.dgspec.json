{"format": 1, "restore": {"C:\\Users\\<USER>\\Downloads\\SultanTrack (1)\\beyond\\BeyondSmartGlass\\BeyondSmartGlass.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Downloads\\SultanTrack (1)\\beyond\\BeyondSmartGlass\\BeyondSmartGlass.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Downloads\\SultanTrack (1)\\beyond\\BeyondSmartGlass\\BeyondSmartGlass.csproj", "projectName": "BeyondSmartGlass", "projectPath": "C:\\Users\\<USER>\\Downloads\\SultanTrack (1)\\beyond\\BeyondSmartGlass\\BeyondSmartGlass.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Downloads\\SultanTrack (1)\\beyond\\BeyondSmartGlass\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net6.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"Microsoft.AspNetCore.Identity.EntityFrameworkCore": {"target": "Package", "version": "[6.0.36, )"}, "Microsoft.EntityFrameworkCore.Design": {"target": "Package", "version": "[6.0.36, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[6.0.36, )"}, "Microsoft.EntityFrameworkCore.Tools": {"target": "Package", "version": "[6.0.36, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\6.0.428\\RuntimeIdentifierGraph.json"}}}}}