/* Please see documentation at https://docs.microsoft.com/aspnet/core/client-side/bundling-and-minification
for details on configuring this project to bundle and minify static web assets. */

a.navbar-brand[b-9mxr9cjhn8] {
  white-space: normal;
  text-align: center;
  word-break: break-all;
}

a[b-9mxr9cjhn8] {
  color: #0077cc;
}

.btn-primary[b-9mxr9cjhn8] {
  color: #fff;
  background-color: #1b6ec2;
  border-color: #1861ac;
}

.nav-pills .nav-link.active[b-9mxr9cjhn8], .nav-pills .show > .nav-link[b-9mxr9cjhn8] {
  color: #fff;
  background-color: #1b6ec2;
  border-color: #1861ac;
}

.border-top[b-9mxr9cjhn8] {
  border-top: 1px solid #e5e5e5;
}
.border-bottom[b-9mxr9cjhn8] {
  border-bottom: 1px solid #e5e5e5;
}

.box-shadow[b-9mxr9cjhn8] {
  box-shadow: 0 .25rem .75rem rgba(0, 0, 0, .05);
}

button.accept-policy[b-9mxr9cjhn8] {
  font-size: 1rem;
  line-height: inherit;
}

.footer[b-9mxr9cjhn8] {
  position: absolute;
  bottom: 0;
  width: 100%;
  white-space: nowrap;
  line-height: 60px;
}
