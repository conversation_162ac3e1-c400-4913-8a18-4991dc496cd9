{"Version": 1, "Hash": "RyTxzePkBsgAV670qMFJ1Qlepz/atEDnOyFCmV8mq9M=", "Source": "BeyondSmartGlass", "BasePath": "_content/BeyondSmartGlass", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Build", "ReferencedProjectsConfiguration": [], "DiscoveryPatterns": [{"Name": "BeyondSmartGlass\\wwwroot", "Source": "BeyondSmartGlass", "ContentRoot": "C:\\Users\\<USER>\\Downloads\\SultanTrack (1)\\beyond\\BeyondSmartGlass\\wwwroot\\", "BasePath": "_content/BeyondSmartGlass", "Pattern": "**"}], "Assets": [{"Identity": "C:\\Users\\<USER>\\Downloads\\SultanTrack (1)\\beyond\\BeyondSmartGlass\\obj\\Debug\\net6.0\\scopedcss\\bundle\\BeyondSmartGlass.styles.css", "SourceId": "BeyondSmartGlass", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Downloads\\SultanTrack (1)\\beyond\\BeyondSmartGlass\\obj\\Debug\\net6.0\\scopedcss\\bundle\\", "BasePath": "_content/BeyondSmartGlass", "RelativePath": "BeyondSmartGlass.styles.css", "AssetKind": "All", "AssetMode": "CurrentProject", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "ScopedCss", "AssetTraitValue": "ApplicationBundle", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Downloads\\SultanTrack (1)\\beyond\\BeyondSmartGlass\\obj\\Debug\\net6.0\\scopedcss\\bundle\\BeyondSmartGlass.styles.css"}, {"Identity": "C:\\Users\\<USER>\\Downloads\\SultanTrack (1)\\beyond\\BeyondSmartGlass\\obj\\Debug\\net6.0\\scopedcss\\projectbundle\\BeyondSmartGlass.bundle.scp.css", "SourceId": "BeyondSmartGlass", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Downloads\\SultanTrack (1)\\beyond\\BeyondSmartGlass\\obj\\Debug\\net6.0\\scopedcss\\projectbundle\\", "BasePath": "_content/BeyondSmartGlass", "RelativePath": "BeyondSmartGlass.bundle.scp.css", "AssetKind": "All", "AssetMode": "Reference", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "ScopedCss", "AssetTraitValue": "ProjectBundle", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Downloads\\SultanTrack (1)\\beyond\\BeyondSmartGlass\\obj\\Debug\\net6.0\\scopedcss\\projectbundle\\BeyondSmartGlass.bundle.scp.css"}, {"Identity": "C:\\Users\\<USER>\\Downloads\\SultanTrack (1)\\beyond\\BeyondSmartGlass\\wwwroot\\css\\site.css", "SourceId": "BeyondSmartGlass", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Downloads\\SultanTrack (1)\\beyond\\BeyondSmartGlass\\wwwroot\\", "BasePath": "_content/BeyondSmartGlass", "RelativePath": "css/site.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\site.css"}, {"Identity": "C:\\Users\\<USER>\\Downloads\\SultanTrack (1)\\beyond\\BeyondSmartGlass\\wwwroot\\favicon.ico", "SourceId": "BeyondSmartGlass", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Downloads\\SultanTrack (1)\\beyond\\BeyondSmartGlass\\wwwroot\\", "BasePath": "_content/BeyondSmartGlass", "RelativePath": "favicon.ico", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\favicon.ico"}, {"Identity": "C:\\Users\\<USER>\\Downloads\\SultanTrack (1)\\beyond\\BeyondSmartGlass\\wwwroot\\js\\site.js", "SourceId": "BeyondSmartGlass", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Downloads\\SultanTrack (1)\\beyond\\BeyondSmartGlass\\wwwroot\\", "BasePath": "_content/BeyondSmartGlass", "RelativePath": "js/site.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\site.js"}, {"Identity": "C:\\Users\\<USER>\\Downloads\\SultanTrack (1)\\beyond\\BeyondSmartGlass\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "SourceId": "BeyondSmartGlass", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Downloads\\SultanTrack (1)\\beyond\\BeyondSmartGlass\\wwwroot\\", "BasePath": "_content/BeyondSmartGlass", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css"}, {"Identity": "C:\\Users\\<USER>\\Downloads\\SultanTrack (1)\\beyond\\BeyondSmartGlass\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "SourceId": "BeyondSmartGlass", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Downloads\\SultanTrack (1)\\beyond\\BeyondSmartGlass\\wwwroot\\", "BasePath": "_content/BeyondSmartGlass", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map"}, {"Identity": "C:\\Users\\<USER>\\Downloads\\SultanTrack (1)\\beyond\\BeyondSmartGlass\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "SourceId": "BeyondSmartGlass", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Downloads\\SultanTrack (1)\\beyond\\BeyondSmartGlass\\wwwroot\\", "BasePath": "_content/BeyondSmartGlass", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css"}, {"Identity": "C:\\Users\\<USER>\\Downloads\\SultanTrack (1)\\beyond\\BeyondSmartGlass\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "SourceId": "BeyondSmartGlass", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Downloads\\SultanTrack (1)\\beyond\\BeyondSmartGlass\\wwwroot\\", "BasePath": "_content/BeyondSmartGlass", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map"}, {"Identity": "C:\\Users\\<USER>\\Downloads\\SultanTrack (1)\\beyond\\BeyondSmartGlass\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "SourceId": "BeyondSmartGlass", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Downloads\\SultanTrack (1)\\beyond\\BeyondSmartGlass\\wwwroot\\", "BasePath": "_content/BeyondSmartGlass", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css"}, {"Identity": "C:\\Users\\<USER>\\Downloads\\SultanTrack (1)\\beyond\\BeyondSmartGlass\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "SourceId": "BeyondSmartGlass", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Downloads\\SultanTrack (1)\\beyond\\BeyondSmartGlass\\wwwroot\\", "BasePath": "_content/BeyondSmartGlass", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map"}, {"Identity": "C:\\Users\\<USER>\\Downloads\\SultanTrack (1)\\beyond\\BeyondSmartGlass\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "SourceId": "BeyondSmartGlass", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Downloads\\SultanTrack (1)\\beyond\\BeyondSmartGlass\\wwwroot\\", "BasePath": "_content/BeyondSmartGlass", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css"}, {"Identity": "C:\\Users\\<USER>\\Downloads\\SultanTrack (1)\\beyond\\BeyondSmartGlass\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "SourceId": "BeyondSmartGlass", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Downloads\\SultanTrack (1)\\beyond\\BeyondSmartGlass\\wwwroot\\", "BasePath": "_content/BeyondSmartGlass", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map"}, {"Identity": "C:\\Users\\<USER>\\Downloads\\SultanTrack (1)\\beyond\\BeyondSmartGlass\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "SourceId": "BeyondSmartGlass", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Downloads\\SultanTrack (1)\\beyond\\BeyondSmartGlass\\wwwroot\\", "BasePath": "_content/BeyondSmartGlass", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css"}, {"Identity": "C:\\Users\\<USER>\\Downloads\\SultanTrack (1)\\beyond\\BeyondSmartGlass\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "SourceId": "BeyondSmartGlass", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Downloads\\SultanTrack (1)\\beyond\\BeyondSmartGlass\\wwwroot\\", "BasePath": "_content/BeyondSmartGlass", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map"}, {"Identity": "C:\\Users\\<USER>\\Downloads\\SultanTrack (1)\\beyond\\BeyondSmartGlass\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "SourceId": "BeyondSmartGlass", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Downloads\\SultanTrack (1)\\beyond\\BeyondSmartGlass\\wwwroot\\", "BasePath": "_content/BeyondSmartGlass", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css"}, {"Identity": "C:\\Users\\<USER>\\Downloads\\SultanTrack (1)\\beyond\\BeyondSmartGlass\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "SourceId": "BeyondSmartGlass", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Downloads\\SultanTrack (1)\\beyond\\BeyondSmartGlass\\wwwroot\\", "BasePath": "_content/BeyondSmartGlass", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map"}, {"Identity": "C:\\Users\\<USER>\\Downloads\\SultanTrack (1)\\beyond\\BeyondSmartGlass\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "SourceId": "BeyondSmartGlass", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Downloads\\SultanTrack (1)\\beyond\\BeyondSmartGlass\\wwwroot\\", "BasePath": "_content/BeyondSmartGlass", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css"}, {"Identity": "C:\\Users\\<USER>\\Downloads\\SultanTrack (1)\\beyond\\BeyondSmartGlass\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "SourceId": "BeyondSmartGlass", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Downloads\\SultanTrack (1)\\beyond\\BeyondSmartGlass\\wwwroot\\", "BasePath": "_content/BeyondSmartGlass", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map"}, {"Identity": "C:\\Users\\<USER>\\Downloads\\SultanTrack (1)\\beyond\\BeyondSmartGlass\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "SourceId": "BeyondSmartGlass", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Downloads\\SultanTrack (1)\\beyond\\BeyondSmartGlass\\wwwroot\\", "BasePath": "_content/BeyondSmartGlass", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css"}, {"Identity": "C:\\Users\\<USER>\\Downloads\\SultanTrack (1)\\beyond\\BeyondSmartGlass\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "SourceId": "BeyondSmartGlass", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Downloads\\SultanTrack (1)\\beyond\\BeyondSmartGlass\\wwwroot\\", "BasePath": "_content/BeyondSmartGlass", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map"}, {"Identity": "C:\\Users\\<USER>\\Downloads\\SultanTrack (1)\\beyond\\BeyondSmartGlass\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "SourceId": "BeyondSmartGlass", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Downloads\\SultanTrack (1)\\beyond\\BeyondSmartGlass\\wwwroot\\", "BasePath": "_content/BeyondSmartGlass", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css"}, {"Identity": "C:\\Users\\<USER>\\Downloads\\SultanTrack (1)\\beyond\\BeyondSmartGlass\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "SourceId": "BeyondSmartGlass", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Downloads\\SultanTrack (1)\\beyond\\BeyondSmartGlass\\wwwroot\\", "BasePath": "_content/BeyondSmartGlass", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map"}, {"Identity": "C:\\Users\\<USER>\\Downloads\\SultanTrack (1)\\beyond\\BeyondSmartGlass\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "SourceId": "BeyondSmartGlass", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Downloads\\SultanTrack (1)\\beyond\\BeyondSmartGlass\\wwwroot\\", "BasePath": "_content/BeyondSmartGlass", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css"}, {"Identity": "C:\\Users\\<USER>\\Downloads\\SultanTrack (1)\\beyond\\BeyondSmartGlass\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "SourceId": "BeyondSmartGlass", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Downloads\\SultanTrack (1)\\beyond\\BeyondSmartGlass\\wwwroot\\", "BasePath": "_content/BeyondSmartGlass", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map"}, {"Identity": "C:\\Users\\<USER>\\Downloads\\SultanTrack (1)\\beyond\\BeyondSmartGlass\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "SourceId": "BeyondSmartGlass", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Downloads\\SultanTrack (1)\\beyond\\BeyondSmartGlass\\wwwroot\\", "BasePath": "_content/BeyondSmartGlass", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css"}, {"Identity": "C:\\Users\\<USER>\\Downloads\\SultanTrack (1)\\beyond\\BeyondSmartGlass\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "SourceId": "BeyondSmartGlass", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Downloads\\SultanTrack (1)\\beyond\\BeyondSmartGlass\\wwwroot\\", "BasePath": "_content/BeyondSmartGlass", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map"}, {"Identity": "C:\\Users\\<USER>\\Downloads\\SultanTrack (1)\\beyond\\BeyondSmartGlass\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "SourceId": "BeyondSmartGlass", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Downloads\\SultanTrack (1)\\beyond\\BeyondSmartGlass\\wwwroot\\", "BasePath": "_content/BeyondSmartGlass", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css"}, {"Identity": "C:\\Users\\<USER>\\Downloads\\SultanTrack (1)\\beyond\\BeyondSmartGlass\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "SourceId": "BeyondSmartGlass", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Downloads\\SultanTrack (1)\\beyond\\BeyondSmartGlass\\wwwroot\\", "BasePath": "_content/BeyondSmartGlass", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map"}, {"Identity": "C:\\Users\\<USER>\\Downloads\\SultanTrack (1)\\beyond\\BeyondSmartGlass\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "SourceId": "BeyondSmartGlass", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Downloads\\SultanTrack (1)\\beyond\\BeyondSmartGlass\\wwwroot\\", "BasePath": "_content/BeyondSmartGlass", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css"}, {"Identity": "C:\\Users\\<USER>\\Downloads\\SultanTrack (1)\\beyond\\BeyondSmartGlass\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "SourceId": "BeyondSmartGlass", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Downloads\\SultanTrack (1)\\beyond\\BeyondSmartGlass\\wwwroot\\", "BasePath": "_content/BeyondSmartGlass", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map"}, {"Identity": "C:\\Users\\<USER>\\Downloads\\SultanTrack (1)\\beyond\\BeyondSmartGlass\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "SourceId": "BeyondSmartGlass", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Downloads\\SultanTrack (1)\\beyond\\BeyondSmartGlass\\wwwroot\\", "BasePath": "_content/BeyondSmartGlass", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css"}, {"Identity": "C:\\Users\\<USER>\\Downloads\\SultanTrack (1)\\beyond\\BeyondSmartGlass\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "SourceId": "BeyondSmartGlass", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Downloads\\SultanTrack (1)\\beyond\\BeyondSmartGlass\\wwwroot\\", "BasePath": "_content/BeyondSmartGlass", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map"}, {"Identity": "C:\\Users\\<USER>\\Downloads\\SultanTrack (1)\\beyond\\BeyondSmartGlass\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "SourceId": "BeyondSmartGlass", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Downloads\\SultanTrack (1)\\beyond\\BeyondSmartGlass\\wwwroot\\", "BasePath": "_content/BeyondSmartGlass", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css"}, {"Identity": "C:\\Users\\<USER>\\Downloads\\SultanTrack (1)\\beyond\\BeyondSmartGlass\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "SourceId": "BeyondSmartGlass", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Downloads\\SultanTrack (1)\\beyond\\BeyondSmartGlass\\wwwroot\\", "BasePath": "_content/BeyondSmartGlass", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map"}, {"Identity": "C:\\Users\\<USER>\\Downloads\\SultanTrack (1)\\beyond\\BeyondSmartGlass\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "SourceId": "BeyondSmartGlass", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Downloads\\SultanTrack (1)\\beyond\\BeyondSmartGlass\\wwwroot\\", "BasePath": "_content/BeyondSmartGlass", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css"}, {"Identity": "C:\\Users\\<USER>\\Downloads\\SultanTrack (1)\\beyond\\BeyondSmartGlass\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "SourceId": "BeyondSmartGlass", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Downloads\\SultanTrack (1)\\beyond\\BeyondSmartGlass\\wwwroot\\", "BasePath": "_content/BeyondSmartGlass", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map"}, {"Identity": "C:\\Users\\<USER>\\Downloads\\SultanTrack (1)\\beyond\\BeyondSmartGlass\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "SourceId": "BeyondSmartGlass", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Downloads\\SultanTrack (1)\\beyond\\BeyondSmartGlass\\wwwroot\\", "BasePath": "_content/BeyondSmartGlass", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js"}, {"Identity": "C:\\Users\\<USER>\\Downloads\\SultanTrack (1)\\beyond\\BeyondSmartGlass\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "SourceId": "BeyondSmartGlass", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Downloads\\SultanTrack (1)\\beyond\\BeyondSmartGlass\\wwwroot\\", "BasePath": "_content/BeyondSmartGlass", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map"}, {"Identity": "C:\\Users\\<USER>\\Downloads\\SultanTrack (1)\\beyond\\BeyondSmartGlass\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "SourceId": "BeyondSmartGlass", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Downloads\\SultanTrack (1)\\beyond\\BeyondSmartGlass\\wwwroot\\", "BasePath": "_content/BeyondSmartGlass", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js"}, {"Identity": "C:\\Users\\<USER>\\Downloads\\SultanTrack (1)\\beyond\\BeyondSmartGlass\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "SourceId": "BeyondSmartGlass", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Downloads\\SultanTrack (1)\\beyond\\BeyondSmartGlass\\wwwroot\\", "BasePath": "_content/BeyondSmartGlass", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map"}, {"Identity": "C:\\Users\\<USER>\\Downloads\\SultanTrack (1)\\beyond\\BeyondSmartGlass\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "SourceId": "BeyondSmartGlass", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Downloads\\SultanTrack (1)\\beyond\\BeyondSmartGlass\\wwwroot\\", "BasePath": "_content/BeyondSmartGlass", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js"}, {"Identity": "C:\\Users\\<USER>\\Downloads\\SultanTrack (1)\\beyond\\BeyondSmartGlass\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "SourceId": "BeyondSmartGlass", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Downloads\\SultanTrack (1)\\beyond\\BeyondSmartGlass\\wwwroot\\", "BasePath": "_content/BeyondSmartGlass", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map"}, {"Identity": "C:\\Users\\<USER>\\Downloads\\SultanTrack (1)\\beyond\\BeyondSmartGlass\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "SourceId": "BeyondSmartGlass", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Downloads\\SultanTrack (1)\\beyond\\BeyondSmartGlass\\wwwroot\\", "BasePath": "_content/BeyondSmartGlass", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js"}, {"Identity": "C:\\Users\\<USER>\\Downloads\\SultanTrack (1)\\beyond\\BeyondSmartGlass\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "SourceId": "BeyondSmartGlass", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Downloads\\SultanTrack (1)\\beyond\\BeyondSmartGlass\\wwwroot\\", "BasePath": "_content/BeyondSmartGlass", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map"}, {"Identity": "C:\\Users\\<USER>\\Downloads\\SultanTrack (1)\\beyond\\BeyondSmartGlass\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "SourceId": "BeyondSmartGlass", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Downloads\\SultanTrack (1)\\beyond\\BeyondSmartGlass\\wwwroot\\", "BasePath": "_content/BeyondSmartGlass", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js"}, {"Identity": "C:\\Users\\<USER>\\Downloads\\SultanTrack (1)\\beyond\\BeyondSmartGlass\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "SourceId": "BeyondSmartGlass", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Downloads\\SultanTrack (1)\\beyond\\BeyondSmartGlass\\wwwroot\\", "BasePath": "_content/BeyondSmartGlass", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map"}, {"Identity": "C:\\Users\\<USER>\\Downloads\\SultanTrack (1)\\beyond\\BeyondSmartGlass\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "SourceId": "BeyondSmartGlass", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Downloads\\SultanTrack (1)\\beyond\\BeyondSmartGlass\\wwwroot\\", "BasePath": "_content/BeyondSmartGlass", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js"}, {"Identity": "C:\\Users\\<USER>\\Downloads\\SultanTrack (1)\\beyond\\BeyondSmartGlass\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "SourceId": "BeyondSmartGlass", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Downloads\\SultanTrack (1)\\beyond\\BeyondSmartGlass\\wwwroot\\", "BasePath": "_content/BeyondSmartGlass", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map"}, {"Identity": "C:\\Users\\<USER>\\Downloads\\SultanTrack (1)\\beyond\\BeyondSmartGlass\\wwwroot\\lib\\bootstrap\\LICENSE", "SourceId": "BeyondSmartGlass", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Downloads\\SultanTrack (1)\\beyond\\BeyondSmartGlass\\wwwroot\\", "BasePath": "_content/BeyondSmartGlass", "RelativePath": "lib/bootstrap/LICENSE", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\LICENSE"}, {"Identity": "C:\\Users\\<USER>\\Downloads\\SultanTrack (1)\\beyond\\BeyondSmartGlass\\wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.js", "SourceId": "BeyondSmartGlass", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Downloads\\SultanTrack (1)\\beyond\\BeyondSmartGlass\\wwwroot\\", "BasePath": "_content/BeyondSmartGlass", "RelativePath": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.js"}, {"Identity": "C:\\Users\\<USER>\\Downloads\\SultanTrack (1)\\beyond\\BeyondSmartGlass\\wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.min.js", "SourceId": "BeyondSmartGlass", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Downloads\\SultanTrack (1)\\beyond\\BeyondSmartGlass\\wwwroot\\", "BasePath": "_content/BeyondSmartGlass", "RelativePath": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.min.js"}, {"Identity": "C:\\Users\\<USER>\\Downloads\\SultanTrack (1)\\beyond\\BeyondSmartGlass\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "SourceId": "BeyondSmartGlass", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Downloads\\SultanTrack (1)\\beyond\\BeyondSmartGlass\\wwwroot\\", "BasePath": "_content/BeyondSmartGlass", "RelativePath": "lib/jquery-validation-unobtrusive/LICENSE.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt"}, {"Identity": "C:\\Users\\<USER>\\Downloads\\SultanTrack (1)\\beyond\\BeyondSmartGlass\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "SourceId": "BeyondSmartGlass", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Downloads\\SultanTrack (1)\\beyond\\BeyondSmartGlass\\wwwroot\\", "BasePath": "_content/BeyondSmartGlass", "RelativePath": "lib/jquery-validation/dist/additional-methods.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js"}, {"Identity": "C:\\Users\\<USER>\\Downloads\\SultanTrack (1)\\beyond\\BeyondSmartGlass\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "SourceId": "BeyondSmartGlass", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Downloads\\SultanTrack (1)\\beyond\\BeyondSmartGlass\\wwwroot\\", "BasePath": "_content/BeyondSmartGlass", "RelativePath": "lib/jquery-validation/dist/additional-methods.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js"}, {"Identity": "C:\\Users\\<USER>\\Downloads\\SultanTrack (1)\\beyond\\BeyondSmartGlass\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "SourceId": "BeyondSmartGlass", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Downloads\\SultanTrack (1)\\beyond\\BeyondSmartGlass\\wwwroot\\", "BasePath": "_content/BeyondSmartGlass", "RelativePath": "lib/jquery-validation/dist/jquery.validate.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js"}, {"Identity": "C:\\Users\\<USER>\\Downloads\\SultanTrack (1)\\beyond\\BeyondSmartGlass\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "SourceId": "BeyondSmartGlass", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Downloads\\SultanTrack (1)\\beyond\\BeyondSmartGlass\\wwwroot\\", "BasePath": "_content/BeyondSmartGlass", "RelativePath": "lib/jquery-validation/dist/jquery.validate.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js"}, {"Identity": "C:\\Users\\<USER>\\Downloads\\SultanTrack (1)\\beyond\\BeyondSmartGlass\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "SourceId": "BeyondSmartGlass", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Downloads\\SultanTrack (1)\\beyond\\BeyondSmartGlass\\wwwroot\\", "BasePath": "_content/BeyondSmartGlass", "RelativePath": "lib/jquery-validation/LICENSE.md", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\LICENSE.md"}, {"Identity": "C:\\Users\\<USER>\\Downloads\\SultanTrack (1)\\beyond\\BeyondSmartGlass\\wwwroot\\lib\\jquery\\dist\\jquery.js", "SourceId": "BeyondSmartGlass", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Downloads\\SultanTrack (1)\\beyond\\BeyondSmartGlass\\wwwroot\\", "BasePath": "_content/BeyondSmartGlass", "RelativePath": "lib/jquery/dist/jquery.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.js"}, {"Identity": "C:\\Users\\<USER>\\Downloads\\SultanTrack (1)\\beyond\\BeyondSmartGlass\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "SourceId": "BeyondSmartGlass", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Downloads\\SultanTrack (1)\\beyond\\BeyondSmartGlass\\wwwroot\\", "BasePath": "_content/BeyondSmartGlass", "RelativePath": "lib/jquery/dist/jquery.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.min.js"}, {"Identity": "C:\\Users\\<USER>\\Downloads\\SultanTrack (1)\\beyond\\BeyondSmartGlass\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "SourceId": "BeyondSmartGlass", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Downloads\\SultanTrack (1)\\beyond\\BeyondSmartGlass\\wwwroot\\", "BasePath": "_content/BeyondSmartGlass", "RelativePath": "lib/jquery/dist/jquery.min.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.min.map"}, {"Identity": "C:\\Users\\<USER>\\Downloads\\SultanTrack (1)\\beyond\\BeyondSmartGlass\\wwwroot\\lib\\jquery\\LICENSE.txt", "SourceId": "BeyondSmartGlass", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Downloads\\SultanTrack (1)\\beyond\\BeyondSmartGlass\\wwwroot\\", "BasePath": "_content/BeyondSmartGlass", "RelativePath": "lib/jquery/LICENSE.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\LICENSE.txt"}]}