html {
  font-size: 14px;
  position: relative;
  min-height: 100%;
}

@media (min-width: 768px) {
  html {
    font-size: 16px;
  }
}

body {
  margin-bottom: 60px;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* RTL Support */
[dir="rtl"] {
  text-align: right;
}

[dir="rtl"] .navbar-nav {
  margin-left: auto;
  margin-right: 0;
}

[dir="rtl"] .dropdown-menu {
  right: 0;
  left: auto;
}

[dir="rtl"] .text-end {
  text-align: left !important;
}

[dir="rtl"] .text-start {
  text-align: right !important;
}

[dir="rtl"] .me-1, [dir="rtl"] .me-2, [dir="rtl"] .me-3 {
  margin-right: 0 !important;
  margin-left: 0.25rem !important;
}

[dir="rtl"] .ms-1, [dir="rtl"] .ms-2, [dir="rtl"] .ms-3 {
  margin-left: 0 !important;
  margin-right: 0.25rem !important;
}

/* Custom Styles */
.navbar-brand {
  font-weight: 600;
}

.card {
  border: none;
  border-radius: 10px;
}

.btn {
  border-radius: 6px;
}

.form-control {
  border-radius: 6px;
}

.table {
  border-radius: 8px;
  overflow: hidden;
}

.badge {
  font-size: 0.75em;
}

/* Status Colors */
.status-pending { background-color: #ffc107; }
.status-approved { background-color: #28a745; }
.status-rejected { background-color: #dc3545; }
.status-active { background-color: #17a2b8; }
.status-completed { background-color: #28a745; }
.status-cancelled { background-color: #6c757d; }

/* Dashboard Cards */
.dashboard-card {
  transition: transform 0.2s;
}

.dashboard-card:hover {
  transform: translateY(-2px);
}

/* Loading Spinner */
.spinner-border-sm {
  width: 1rem;
  height: 1rem;
}